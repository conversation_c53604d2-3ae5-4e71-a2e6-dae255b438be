// src/components/SocketIoChat/SupabaseOAuthChatSetup.tsx

import React, { useState, useContext, useEffect } from "react";
import {
  X,
  Check,
  Loader2,
  Plus,
  Search,
  Eye,
  EyeOff,
  AlertTriangle,
  Refresh<PERSON>cw,
  Copy,
} from "lucide-react";
import {
  connectToSupabase,
  listSupabaseOrg,
  listSupabaseProjects,
  createSupabaseProject,
  updateSupabaseDB,
  checkBootstrapStatus,
  getSupabaseProjectStatusDisplay,
  checkSupabaseProjectReadiness
} from "@/utils/api";
import { AlertContext } from "@/components/NotificationAlertService/AlertList";

// Props for the OAuth setup component
interface SupabaseOAuthChatSetupProps {
  externalActionRequest: any;
  parentMessageId: string;
  onComplete: (result: any) => void;
  onClose: () => void;
  projectId: string; // Chat panel project ID
}

// Represents a Supabase project
interface SupabaseProject {
  id: string;
  name: string;
  region: string;
  dashboard_url: string;
  status?: string;
  is_ready?: boolean;
  can_continue?: boolean;
  status_message?: string;
  ui_status?: "success" | "loading" | "warning" | "error";
  description?: string;
}

// Defines the steps in the OAuth flow
type OAuthStep =
  | "oauth_connect"
  | "select_project"
  | "create_project"
  | "configure_database"
  | "completing";

// Main component
const SupabaseOAuthChatSetup: React.FC<SupabaseOAuthChatSetupProps> = ({
  onComplete,
  onClose,
  projectId,
}) => {
  const [currentStep, setCurrentStep] = useState<OAuthStep>("oauth_connect");
  const [projects, setProjects] = useState<SupabaseProject[]>([]);
  const [selectedProject, setSelectedProject] =
    useState<SupabaseProject | null>(null);
  const [dbPassword, setDbPassword] = useState("");
  const [dbPasswordError, setDbPasswordError] = useState("");
  const [showDbPassword, setShowDbPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");

  // Project creation state
  const [isCreatingProject, setIsCreatingProject] = useState(false);
  const [newProject, setNewProject] = useState({
    name: "",
    organization_id: "",
    region: "us-east-1", // Default to US East (N. Virginia)
    db_password: "",
  });
  const [organizations, setOrganizations] = useState([]);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [checkingReadiness, setCheckingReadiness] = useState<string | null>(null);
  const [showPassword, setShowPassword] = useState(false);
  const [copiedPassword, setCopiedPassword] = useState(false);

  const { showAlert } = useContext(AlertContext);

  // OAuth connection handler
  const handleOAuthConnect = async () => {
    setIsLoading(true);
    try {
      const response = await connectToSupabase(projectId);

      if (response && response.auth_url) {
        const popup = window.open(
          response.auth_url,
          "supabase_oauth",
          "width=500,height=600,scrollbars=yes,resizable=yes"
        );

        const checkPopupClosed = setInterval(async () => {
          if (popup?.closed) {
            clearInterval(checkPopupClosed);
            try {
              // Show loading state while fetching projects
              setCurrentStep("select_project");
              setIsLoading(true);
              await fetchUserProjects();
              setIsLoading(false);
            } catch (fetchError) {
              console.error("Failed to fetch projects:", fetchError);
              showAlert("Failed to fetch Supabase projects", "error");
              setIsLoading(false);
            }
          }
        }, 1000);

        setTimeout(() => {
          if (!popup?.closed) {
            clearInterval(checkPopupClosed);
            showAlert("OAuth timeout - please try again", "warning");
          }
        }, 120000); // 2 minutes timeout
      }
    } catch (error) {
      console.error("Error connecting to Supabase:", error);
      showAlert("Failed to initiate Supabase connection", "error");
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch user's Supabase projects and organizations
  const fetchUserProjects = async () => {
    try {
      // Get organizations
      const organizationsResponse = await listSupabaseOrg(projectId);

      if (!organizationsResponse?.data?.length) {
        setProjects([]);
        setOrganizations([]);
        return;
      }

      // Store organizations for project creation
      setOrganizations(organizationsResponse.data);

      // Set default organization for new project
      if (organizationsResponse.data.length > 0) {
        setNewProject((prev) => ({
          ...prev,
          organization_id: organizationsResponse.data[0].id,
        }));
      }

      // Get projects for the first organization
      const organizationId = organizationsResponse.data[0].id;
      const projectsResponse = await listSupabaseProjects(
        projectId,
        organizationId,
        true
      );

      if (projectsResponse?.success && projectsResponse?.data) {
        // Sort projects: newly created first, then by name
        const sortedProjects = projectsResponse.data.sort(
          (a: SupabaseProject, b: SupabaseProject) => {
            // Prioritize ACTIVE_HEALTHY projects
            if (a.status === "ACTIVE_HEALTHY" && b.status !== "ACTIVE_HEALTHY")
              return -1;
            if (b.status === "ACTIVE_HEALTHY" && a.status !== "ACTIVE_HEALTHY")
              return 1;

            // Then sort by name
            return a.name.localeCompare(b.name);
          }
        );

        setProjects(sortedProjects);
      } else {
        setProjects([]);
      }
    } catch (error) {
      console.error("Error fetching projects:", error);
      setProjects([]);
      setOrganizations([]);
    }
  };

  // Handle project selection
  const handleProjectSelect = async (project: SupabaseProject) => {
    setSelectedProject(project);

    if (!project.can_continue) {
      // Project is not ready, start checking readiness
      setCheckingReadiness(project.id);
      await checkProjectReadiness(project);
    } else if (project.is_ready) {
      setCurrentStep("configure_database");
    }
  };

  // Check if project is ready
  const checkProjectReadiness = async (project: SupabaseProject) => {
    try {
      const response = await checkSupabaseProjectReadiness(projectId, project.id);

      if (response.readiness?.can_continue) {
        // Update the project in the list
        setProjects(prev => prev.map(p =>
          p.id === project.id
            ? {
                ...p,
                ...response.readiness,
                is_ready: response.readiness.can_continue,
                can_continue: response.readiness.can_continue
              }
            : p
        ));

        // Update selected project
        setSelectedProject(prev => prev ? {
          ...prev,
          ...response.readiness,
          is_ready: response.readiness.can_continue,
          can_continue: response.readiness.can_continue
        } : null);

        setCheckingReadiness(null);
        setCurrentStep("configure_database");
      } else {
        // Still not ready, check again in 10 seconds
        setTimeout(() => checkProjectReadiness(project), 10000);
      }
    } catch (error) {
      console.error('Failed to check readiness:', error);
      setCheckingReadiness(null);
    }
  };

  // Generate a strong password
  const generatePassword = () => {
    const length = 16;
    const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*";
    let password = "";
    for (let i = 0; i < length; i++) {
      password += charset.charAt(Math.floor(Math.random() * charset.length));
    }
    setNewProject({ ...newProject, db_password: password });
    setShowPassword(true);
  };

  // Copy password to clipboard
  const copyPassword = async () => {
    if (newProject.db_password) {
      try {
        await navigator.clipboard.writeText(newProject.db_password);
        setCopiedPassword(true);
        setTimeout(() => setCopiedPassword(false), 2000);
      } catch (err) {
        console.error('Failed to copy password:', err);
      }
    }
  };

  // // Handle project creation
  // const handleCreateProject = async () => {
  //     if (!newProject.name.trim()) {
  //         showAlert('Project name is required', 'error');
  //         return;
  //     }

  //     setIsCreatingProject(true);
  //     try {
  //         const projectData = {
  //             name: newProject.name,
  //             organization_id: newProject.organization_id,
  //             region: newProject.region,
  //             db_password: newProject.db_password || undefined // Let backend generate if empty
  //         };

  //         const result = await createSupabaseProject(projectId, projectData);

  //         if (result?.success) {
  //             showAlert(`Project "${newProject.name}" created successfully!`, 'success');

  //             // Create a project object for the newly created project
  //             const createdProject: SupabaseProject = {
  //                 id: result.data.project_id,
  //                 name: newProject.name,
  //                 region: newProject.region,
  //                 dashboard_url: result.data.dashboard_url,
  //                 status: 'INITIALIZING',
  //                 is_ready: false,
  //                 can_continue: false,
  //                 status_message: 'Setting up...',
  //                 ui_status: 'loading',
  //                 description: 'Supabase project setup may take a few seconds. Please wait.'
  //             };

  //             setSelectedProject(createdProject);
  //             setDbPassword(newProject.db_password || result.data.db_password || '');
  //             setCurrentStep('configure_database');
  //         } else {
  //             throw new Error(result?.error || 'Failed to create project');
  //         }
  //     } catch (error) {
  //         console.error('Error creating project:', error);
  //         showAlert('Failed to create project', 'error');
  //     } finally {
  //         setIsCreatingProject(false);
  //     }
  // };

 

  // Add this useEffect to both components for real-time status updates
  useEffect(() => {
    let intervalId: NodeJS.Timeout;

    if (selectedProject && !selectedProject.is_ready) {
      intervalId = setInterval(async () => {
        const isReady = await checkBootstrapStatus(
          projectId,
          selectedProject.id
        );

        if (isReady) {
          setSelectedProject((prev) =>
            prev
              ? {
                  ...prev,
                  is_ready: true,
                  can_continue: true,
                  status_message: "Ready to use",
                  ui_status: "success",
                  description:
                    "Project is fully initialized and ready for database operations",
                }
              : null
          );

          setProjects((prev) =>
            prev.map((p) =>
              p.id === selectedProject.id
                ? {
                    ...p,
                    is_ready: true,
                    can_continue: true,
                    status_message: "Ready to use",
                    ui_status: "success",
                  }
                : p
            )
          );

          showAlert("Project is now ready to use!", "success");
        }
      }, 5000); // Check every 5 seconds
    }

    return () => {
      if (intervalId) clearInterval(intervalId);
    };
  }, [selectedProject?.id, selectedProject?.is_ready]);
  

  const handleCreateProject = async () => {
    if (!newProject.name.trim()) {
        showAlert("Project name is required", "error");
        return;
    }

    setIsCreatingProject(true);
    try {
        const organizationsResponse = await listSupabaseOrg(projectId);

        if (!organizationsResponse?.data?.length) {
            throw new Error("Unable to fetch organizations");
        }

        const projectData = {
            name: newProject.name,
            organization_id: organizationsResponse.data[0].id,
            region: newProject.region,
            db_password: newProject.db_password || undefined,
        };

        // Use standard project creation (bootstrap will happen in update_db)
        const result = await createSupabaseProject(projectId, projectData);

        if (result?.success) {
            const createdProject: SupabaseProject = {
                id: result.data.project_id,
                name: newProject.name,
                region: newProject.region,
                dashboard_url: result.data.dashboard_url,
                status: "ACTIVE_HEALTHY",
                is_ready: false, // Will be updated after database config
                can_continue: false,
                status_message: "Created, pending database setup",
                ui_status: "warning",
                description: "Project created successfully. Database setup will happen when you configure the connection."
            };

            setSelectedProject(createdProject);
            setDbPassword(newProject.db_password || result.data.db_password || "");
            setCurrentStep("configure_database");

            showAlert(`Project "${newProject.name}" created! Please configure database.`, "success");
        } else {
            throw new Error(result?.error || "Failed to create project");
        }
    } catch (error) {
        console.error("Error creating project:", error);
        showAlert("Failed to create project", "error");
    } finally {
        setIsCreatingProject(false);
    }
};
  // Handle database configuration completion
  const handleDatabaseComplete = async () => {
    if (!selectedProject?.id || !dbPassword.trim()) {
      setDbPasswordError("Database password is required");
      return;
    }

    if (dbPassword.length < 8) {
      setDbPasswordError("Password must be at least 8 characters");
      return;
    }

    setCurrentStep("completing");
    setIsLoading(true);

    try {
      // Bootstrap the database connection
      const bootstrapResult = await updateSupabaseDB(
        projectId,
        selectedProject.id,
      );

      if (bootstrapResult?.success) {
        // Prepare the completion result
        console.log("bootstrapResult", bootstrapResult);
        const result = {
          selectedProject,
          apiUrl: `https://${selectedProject.id}.supabase.co`,
          databaseUrl: `postgresql://postgres:${dbPassword}@db.${selectedProject.id}.supabase.co:5432/postgres`,
          anonKey: bootstrapResult.data?.anon_key || "PLACEHOLDER_ANON_KEY",
          serviceRoleKey:
            bootstrapResult.data?.service_role_key || "PLACEHOLDER_SERVICE_KEY",
        };

        onComplete(result);
      } else {
        throw new Error(
          bootstrapResult?.error || "Failed to bootstrap database"
        );
      }
    } catch (error) {
      console.error("Error completing database setup:", error);
      showAlert("Failed to complete database setup", "error");
      setCurrentStep("configure_database");
    } finally {
      setIsLoading(false);
    }
  };

  // Handle refresh projects
  const handleRefreshProjects = async () => {
    setIsRefreshing(true);
    try {
      await fetchUserProjects();
      showAlert("Projects refreshed successfully", "success");
    } catch (error) {
      console.error("Error refreshing projects:", error);
      showAlert("Failed to refresh projects", "error");
    } finally {
      setIsRefreshing(false);
    }
  };

  // Filter projects based on search query
  const filteredProjects = projects.filter((project) =>
    project.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-6 mt-4 max-w-2xl">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900">
          Connect to Supabase
        </h3>
        <button onClick={onClose} className="text-gray-400 hover:text-gray-600">
          <X className="h-5 w-5" />
        </button>
      </div>

      {/* Step Indicator */}
      <div className="flex items-center mb-6">
        <div
          className={`flex items-center ${
            currentStep === "oauth_connect" ? "text-blue-600" : "text-green-600"
          }`}
        >
          <div
            className={`w-8 h-8 rounded-full flex items-center justify-center ${
              currentStep === "oauth_connect" ? "bg-blue-100" : "bg-green-100"
            }`}
          >
            {currentStep === "oauth_connect" ? (
              "1"
            ) : (
              <Check className="h-4 w-4" />
            )}
          </div>
          <span className="ml-2 text-sm font-medium">Connect</span>
        </div>

        <div className="flex-1 h-px bg-gray-200 mx-4"></div>

        <div
          className={`flex items-center ${
            ["select_project", "create_project"].includes(currentStep)
              ? "text-blue-600"
              : ["configure_database", "completing"].includes(currentStep)
              ? "text-green-600"
              : "text-gray-400"
          }`}
        >
          <div
            className={`w-8 h-8 rounded-full flex items-center justify-center ${
              ["select_project", "create_project"].includes(currentStep)
                ? "bg-blue-100"
                : ["configure_database", "completing"].includes(currentStep)
                ? "bg-green-100"
                : "bg-gray-100"
            }`}
          >
            {["select_project", "create_project"].includes(currentStep) ? (
              "2"
            ) : ["configure_database", "completing"].includes(currentStep) ? (
              <Check className="h-4 w-4" />
            ) : (
              "2"
            )}
          </div>
          <span className="ml-2 text-sm font-medium">Select</span>
        </div>

        <div className="flex-1 h-px bg-gray-200 mx-4"></div>

        <div
          className={`flex items-center ${
            currentStep === "configure_database"
              ? "text-blue-600"
              : currentStep === "completing"
              ? "text-green-600"
              : "text-gray-400"
          }`}
        >
          <div
            className={`w-8 h-8 rounded-full flex items-center justify-center ${
              currentStep === "configure_database"
                ? "bg-blue-100"
                : currentStep === "completing"
                ? "bg-green-100"
                : "bg-gray-100"
            }`}
          >
            {currentStep === "configure_database" ? (
              "3"
            ) : currentStep === "completing" ? (
              <Check className="h-4 w-4" />
            ) : (
              "3"
            )}
          </div>
          <span className="ml-2 text-sm font-medium">Configure</span>
        </div>
      </div>

      {/* Step Content */}
      {currentStep === "oauth_connect" && (
        <div className="text-center">
          <div className="mb-4">
            <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg width="32" height="32" viewBox="0 0 112 113" fill="none">
                <path
                  d="M63.7076 110.284C60.8481 113.885 55.0502 111.912 54.9813 107.314L53.9738 40.0627L99.1935 40.0627C107.384 40.0627 111.952 49.5228 106.859 55.9374L63.7076 110.284Z"
                  fill="#3ECF8E"
                />
                <path
                  d="M45.317 2.07103C48.1765 -1.53037 53.9745 0.442937 54.0434 5.041L54.4849 72.2922H9.83113C1.64038 72.2922 -2.92775 62.8321 2.1655 56.4175L45.317 2.07103Z"
                  fill="#3ECF8E"
                />
              </svg>
            </div>
            <h4 className="text-xl font-semibold text-gray-900 mb-2">
              Connect to Supabase
            </h4>
            <p className="text-gray-600 mb-6">
              We'll securely connect to your Supabase account to set up your
              project automatically.
            </p>
          </div>

          <button
            onClick={handleOAuthConnect}
            disabled={isLoading}
            className="bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center mx-auto"
          >
            {isLoading ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
                Connecting...
              </>
            ) : (
              "Connect to Supabase"
            )}
          </button>
        </div>
      )}

      {currentStep === "select_project" && (
        <div>
          <div className="flex items-center justify-between mb-4">
            <h4 className="text-lg font-semibold text-gray-900">
              Select Supabase Project
            </h4>
            <div className="flex items-center gap-2">
              <button
                onClick={handleRefreshProjects}
                disabled={isRefreshing}
                className="flex items-center gap-2 px-3 py-2 text-sm bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                title="Refresh project list"
              >
                <RefreshCcw
                  className={`h-4 w-4 ${isRefreshing ? "animate-spin" : ""}`}
                />
                {isRefreshing ? "Refreshing..." : "Refresh"}
              </button>
              <button
                onClick={() => setShowCreateForm(!showCreateForm)}
                className="flex items-center gap-2 px-3 py-2 text-sm bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
              >
                <Plus className="h-4 w-4" />
                Create New
              </button>
            </div>
          </div>

          {/* Create Project Form */}
          {showCreateForm && (
            <div className="mb-6 p-4 border border-gray-200 rounded-lg bg-gray-50">
              <h5 className="font-medium text-gray-900 mb-3">
                Create New Project
              </h5>
              <div className="space-y-3">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Project Name
                  </label>
                  <input
                    type="text"
                    value={newProject.name}
                    onChange={(e) =>
                      setNewProject((prev) => ({
                        ...prev,
                        name: e.target.value,
                      }))
                    }
                    placeholder="my-awesome-project"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Database Password
                  </label>
                  <div className="relative">
                    <input
                      type={showPassword ? "text" : "password"}
                      value={newProject.db_password}
                      onChange={(e) =>
                        setNewProject((prev) => ({
                          ...prev,
                          db_password: e.target.value,
                        }))
                      }
                      placeholder="Enter database password"
                      className="w-full px-3 py-2 pr-24 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                    />
                    <div className="absolute right-2 top-1/2 -translate-y-1/2 flex items-center space-x-1">
                      {newProject.db_password && (
                        <button
                          type="button"
                          onClick={copyPassword}
                          className="p-1.5 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded transition-colors"
                          title="Copy password"
                        >
                          {copiedPassword ? (
                            <Check size={16} className="text-green-500" />
                          ) : (
                            <Copy size={16} />
                          )}
                        </button>
                      )}
                      <button
                        type="button"
                        onClick={() => setShowPassword(!showPassword)}
                        className="p-1.5 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded transition-colors"
                        title={showPassword ? "Hide password" : "Show password"}
                      >
                        {showPassword ? <EyeOff size={16} /> : <Eye size={16} />}
                      </button>
                    </div>
                  </div>
                  <p className="text-gray-500 text-sm mt-1">
                    This is the password for your Postgres database. 
                    <span
                      onClick={generatePassword}
                      className="text-green-600 underline cursor-pointer ml-1 hover:text-green-700"
                    >
                      Generate a password
                    </span>
                  </p>
                </div>

                <div className="grid grid-cols-2 gap-3">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Organization
                    </label>
                    <select
                      value={newProject.organization_id}
                      onChange={(e) =>
                        setNewProject((prev) => ({
                          ...prev,
                          organization_id: e.target.value,
                        }))
                      }
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                    >
                      {organizations.map((org: any) => (
                        <option key={org.id} value={org.id}>
                          {org.name}
                        </option>
                      ))}
                    </select>
                  </div>

                  {/* Region selection commented out - defaulting to US East (N. Virginia)
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Region
                    </label>
                    <select
                      value={newProject.region}
                      onChange={(e) =>
                        setNewProject((prev) => ({
                          ...prev,
                          region: e.target.value,
                        }))
                      }
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                    >
                      <option value="">Select a region</option>
                      <option value="us-east-1">🇺🇸 US East (N. Virginia)</option>
                      <option value="us-west-1">🇺🇸 US West (N. California)</option>
                      <option value="ca-central-1">🇨🇦 Canada (Central)</option>
                      <option value="sa-east-1">🇧🇷 South America (São Paulo)</option>
                      <option value="eu-west-1">🇮🇪 Europe (Ireland)</option>
                      <option value="eu-west-2">🇬🇧 Europe (London)</option>
                      <option value="eu-west-3">🇫🇷 Europe (Paris)</option>
                      <option value="eu-central-1">🇩🇪 Europe (Frankfurt)</option>
                      <option value="eu-north-1">🇸🇪 Europe (Stockholm)</option>
                      <option value="ap-south-1">🇮🇳 Asia Pacific (Mumbai)</option>
                      <option value="ap-northeast-1">🇯🇵 Asia Pacific (Tokyo)</option>
                      <option value="ap-northeast-2">🇰🇷 Asia Pacific (Seoul)</option>
                      <option value="ap-southeast-1">🇸🇬 Asia Pacific (Singapore)</option>
                      <option value="ap-southeast-2">🇦🇺 Oceania (Sydney)</option>
                    </select>
                  </div>
                  */}
                </div>

                <div className="flex gap-2">
                  <button
                    onClick={() => setShowCreateForm(false)}
                    className="px-4 py-2 text-gray-600 bg-gray-200 rounded-lg hover:bg-gray-300 transition-colors"
                    disabled={isCreatingProject}
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleCreateProject}
                    disabled={!newProject.name.trim() || isCreatingProject}
                    className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
                  >
                    {isCreatingProject ? (
                      <>
                        <Loader2 className="h-4 w-4 animate-spin" />
                        Creating...
                      </>
                    ) : (
                      "Create Project"
                    )}
                  </button>
                </div>
              </div>
            </div>
          )}

          {projects.length > 0 && !showCreateForm && (
            <div className="mb-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <input
                  type="text"
                  placeholder="Search projects..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                />
              </div>
            </div>
          )}

          <div className="max-h-64 overflow-y-auto space-y-2">
            {isLoading ? (
              <div className="text-center py-8">
                <Loader2 className="h-8 w-8 text-green-600 animate-spin mx-auto mb-2" />
                <p className="text-gray-600">
                  Loading your Supabase projects...
                </p>
              </div>
            ) : filteredProjects.length > 0 ? (
              filteredProjects.map((project) => {
                const statusDisplay = getSupabaseProjectStatusDisplay(project);
                const isSelected = selectedProject?.id === project.id;
                const isCheckingThis = checkingReadiness === project.id;

                return (
                  <div
                    key={project.id}
                    onClick={() => handleProjectSelect(project)}
                    className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                      isSelected
                        ? "border-green-500 bg-green-50"
                        : "border-gray-200 hover:border-gray-300 hover:bg-gray-50"
                    }`}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <h5 className="font-medium text-gray-900">
                          {project.name}
                        </h5>
                        <p className="text-sm text-gray-500">
                          Region: {project.region}
                        </p>
                        <div className="flex items-center gap-2 mt-1">
                          {isCheckingThis && (
                            <Loader2 className="h-4 w-4 animate-spin text-amber-500" />
                          )}
                          <div
                            className="px-2 py-1 rounded text-xs font-medium flex items-center gap-1"
                            style={{
                              backgroundColor: statusDisplay.bgColor,
                              color: statusDisplay.textColor
                            }}
                          >
                            <span>{statusDisplay.icon}</span>
                            <span>{statusDisplay.message}</span>
                          </div>
                        </div>
                        {!statusDisplay.canContinue && (
                          <div className="text-xs text-gray-500 mt-1">
                            {statusDisplay.description}
                          </div>
                        )}
                      </div>
                      <div className="flex items-center">
                        {project.ui_status === "success" && (
                          <Check className="h-5 w-5 text-green-500" />
                        )}
                        {project.ui_status === "loading" && (
                          <Loader2 className="h-5 w-5 text-blue-500 animate-spin" />
                        )}
                        {project.ui_status === "warning" && (
                          <AlertTriangle className="h-5 w-5 text-yellow-500" />
                        )}
                        {project.ui_status === "error" && (
                          <X className="h-5 w-5 text-red-500" />
                        )}
                      </div>
                    </div>
                  </div>
                );
              })
            ) : (
              <div className="text-center py-8">
                {projects.length === 0 ? (
                  <div>
                    <p className="text-gray-500 mb-3">
                      No Supabase projects found
                    </p>
                  </div>
                ) : (
                  <p className="text-gray-500">No projects match your search</p>
                )}
              </div>
            )}
          </div>
          
          {/* Continue button for selected project */}
          {selectedProject && (
            <div className="mt-6 flex justify-end">
              <button
                onClick={() => setCurrentStep("configure_database")}
                disabled={!selectedProject.can_continue || checkingReadiness === selectedProject.id}
                className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
              >
                {checkingReadiness === selectedProject.id ? (
                  <>
                    <Loader2 className="h-4 w-4 animate-spin" />
                    Checking readiness...
                  </>
                ) : (
                  "Continue"
                )}
              </button>
            </div>
          )}
        </div>
      )}

      {currentStep === "configure_database" && selectedProject && (
        <div>
          <h4 className="text-lg font-semibold text-gray-900 mb-4">
            Database Configuration
          </h4>

          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
            <div className="flex items-start">
              <div className="flex-shrink-0">
                <Check className="h-5 w-5 text-blue-600 mt-0.5" />
              </div>
              <div className="ml-3">
                <h5 className="font-medium text-blue-900">Selected Project</h5>
                <p className="text-blue-700">{selectedProject.name}</p>
                <p className="text-sm text-blue-600">
                  Region: {selectedProject.region}
                </p>
              </div>
            </div>
          </div>

          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Database Password
              </label>
              <div className="relative">
                <input
                  type={showDbPassword ? "text" : "password"}
                  value={dbPassword}
                  onChange={(e) => {
                    setDbPassword(e.target.value);
                    setDbPasswordError("");
                  }}
                  placeholder="Enter your database password"
                  className={`w-full px-3 py-2 pr-10 border rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent ${
                    dbPasswordError ? "border-red-300" : "border-gray-300"
                  }`}
                />
                <button
                  type="button"
                  onClick={() => setShowDbPassword(!showDbPassword)}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                >
                  {showDbPassword ? (
                    <EyeOff className="h-4 w-4" />
                  ) : (
                    <Eye className="h-4 w-4" />
                  )}
                </button>
              </div>
              {dbPasswordError && (
                <p className="text-red-600 text-sm mt-1">{dbPasswordError}</p>
              )}
              <p className="text-gray-500 text-sm mt-1">
                This is the password you set when creating your Supabase project
              </p>
            </div>

            <button
              onClick={handleDatabaseComplete}
              disabled={!dbPassword.trim() || isLoading}
              className="w-full bg-green-600 text-white py-3 rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
            >
              {isLoading ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  Configuring...
                </>
              ) : (
                "Complete Setup"
              )}
            </button>
          </div>
        </div>
      )}

      {currentStep === "completing" && (
        <div className="text-center">
          <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <Loader2 className="h-8 w-8 text-green-600 animate-spin" />
          </div>
          <h4 className="text-lg font-semibold text-gray-900 mb-2">
            Completing Setup
          </h4>
          <p className="text-gray-600">
            Configuring your Supabase connection...
          </p>
        </div>
      )}
    </div>
  );
};

export default SupabaseOAuthChatSetup;
