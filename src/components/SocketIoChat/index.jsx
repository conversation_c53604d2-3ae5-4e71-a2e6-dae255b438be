import React, {
  useEffect,
  useState,
  useRef,
  useCallback,
  useMemo
} from 'react';
import Cookies from 'js-cookie';
import { useCodeGeneration } from '../Context/CodeGenerationContext';
import { getUserAvatar, getKaviaAvatar } from '@/utils/avatarUtils';
import { useUser } from '@/components/Context/UserContext';
import { renderHTML } from '@/utils/helpers';
import { useSearchParams } from 'next/navigation';
import { formatDateTime } from '@/utils/datetime';
import ChatInput from "./ChatInput";
import ChatLoader from "@/components/Loaders/chatLoader";
import ConfirmationModal from '../Modal/ConfirmationModal';

// Import extracted components and hooks
import {  RollbackProgressBar } from './components/LoadingComponents';
import { useRollback } from './hooks/useRollback';
import { useScrollManagement } from './hooks/useScrollManagement';
import { useFileOperations } from './hooks/useFileOperations';
import { useWebSocketMessages } from './hooks/useWebSocketMessages';
import MessageElement from './components/MessageElement';
import CostWarningElement from './components/CostWarningElement';

// Import icons for external service modal
import { X, ExternalLink, Settings, Check } from 'lucide-react';

// Import OAuth setup component
import SupabaseOAuthChatSetup from './SupabaseOAuthChatSetup';

import './styles.css';

const ChatInterface = ({ wsUrl, isPanelExpanded, deploymentStatus, availableModels: propAvailableModels, isLoadingModels: propIsLoadingModels, onModelSelect: propOnModelSelect, isStopped }) => {
  const searchParams = useSearchParams();

  // STATE VARIABLES
  const [deploymentStatusMap, setDeploymentStatusMap] = useState({});
  const [inputValue, setInputValue] = useState('');
  const [messages, setMessages] = useState([]);
  const [isInputEnabled, setIsInputEnabled] = useState(true);
  const lastMessageCountRef = useRef(0);
  const [attachments, setAttachments] = useState([]);
  const [attachedFiles, setAttachedFiles] = useState([]);
  const [uploadedAttachments, setUploadedAttachments] = useState([]);
  const [openAccordions, setOpenAccordions] = useState({});
  const [inputFocused, setInputFocused] = useState(false);
  const isComponentMountedRef = useRef(true);
  
  // Priority handling for historical vs socket messages
  const [isLoadingHistoricalMessages, setIsLoadingHistoricalMessages] = useState(false);
  const [pendingSocketMessages, setPendingSocketMessages] = useState([]);
  const historicalMessagesLoadedRef = useRef(false);
  const socketMessageQueueRef = useRef([]);
  const { name, email } = useUser();
  
  // GET CONTEXT VALUES
  const {
    wsConnection,
    messages: initialCtxMessages,
    textAreaRef,
    messagesContainerRef,
    activeReplyTo,
    setActiveReplyTo,
    hasNewMessages,
    setHasNewMessages,
    autoScroll,
    setAutoScroll,
    isReady,
    setIsReady,
    isAiTyping,
    setIsAiTyping,
    taskStatus,
    fetchInitialMessages
  } = useCodeGeneration();

  // GET USER INFO
  const idToken = Cookies.get('idToken');
  const userId = Cookies.get('userId');

  // EXTERNAL SERVICE CONFIGURATION HOOK
  const useExternalServiceConfig = (wsConnection, userId) => {
    const [modalState, setModalState] = useState({
      isOpen: false,
      externalActionRequest: null,
      parentMessageId: null
    });

    // OAuth setup state
    const [oauthSetupState, setOauthSetupState] = useState({
      isOpen: false,
      externalActionRequest: null,
      parentMessageId: null
    });

    // Track configured services
    const [configuredServices, setConfiguredServices] = useState(new Set());

    const showConfigModal = (externalActionRequest, parentMessageId) => {
      // Auto-convert Supabase requests to OAuth flow
      let processedRequest = { ...externalActionRequest };
      if (processedRequest.service_name === 'supabase' && !processedRequest.oauth_flow) {
        processedRequest = {
          ...processedRequest,
          action_type: 'setup_service_oauth',
          oauth_flow: true
        };
      }

      // Check if this is an OAuth flow
      if (processedRequest.oauth_flow) {
        setOauthSetupState({
          isOpen: true,
          externalActionRequest: processedRequest,
          parentMessageId
        });
      } else {
        setModalState({
          isOpen: true,
          externalActionRequest: processedRequest,
          parentMessageId
        });
      }
    };

    const closeConfigModal = () => {
      setModalState({
        isOpen: false,
        externalActionRequest: null,
        parentMessageId: null
      });
    };

    const closeOAuthSetup = () => {
      setOauthSetupState({
        isOpen: false,
        externalActionRequest: null,
        parentMessageId: null
      });
    };

    const handleOAuthComplete = async (result) => {
      try {
        const extra = {
          type: "setup_service_oauth_complete",
          service_name: oauthSetupState.externalActionRequest.service_name,
          action_type: "setup_service_oauth",
          project_id: result.selectedProject.id,
          project_name: result.selectedProject.name,
          api_url: result.apiUrl,
          database_url: result.databaseUrl,
          vars: [
            { name: "SUPABASE_URL", value: result.apiUrl },
            { name: "SUPABASE_KEY", value: result.anonKey }
          ]
        };

        const responseContent = `🎉 **Supabase Connected Successfully!**

**Project Details:**
- **Name:** ${result.selectedProject.name}
- **Region:** ${result.selectedProject.region}
- **Project ID:** ${result.selectedProject.id}

**Environment Variables Configured:**
\`\`\`
SUPABASE_URL=${result.apiUrl}
SUPABASE_KEY=${result.anonKey.substring(0, 20)}...
\`\`\`

Your project is now ready to use Supabase! The environment variables have been automatically added to your project configuration.`;

        await submitConfiguration(responseContent, [], extra, oauthSetupState.parentMessageId);

        // Mark service as configured
        setConfiguredServices(prev => new Set([...prev, oauthSetupState.externalActionRequest.service_name]));

        closeOAuthSetup();
      } catch (error) {
        console.error('Error completing OAuth setup:', error);
      }
    };

    const submitConfiguration = async (content, attachments, extra, parentId) => {
      return new Promise((resolve, reject) => {
        try {
          if (wsConnection?.readyState === WebSocket.OPEN) {
            const messagePayload = {
              type: 'send_message',
              content,
              attachments: attachments || [],
              extra: extra || {},
              parent_id: parentId,
              user_id: userId
            };

            wsConnection.send(JSON.stringify(messagePayload));

            // Mark service as configured for both manual and OAuth setup
            if (extra?.type === 'setup_service' && extra?.service_name) {
              setConfiguredServices(prev => new Set([...prev, extra.service_name]));
            }

            resolve(messagePayload);
          } else {
            reject(new Error('WebSocket connection not available'));
          }
        } catch (error) {
          reject(error);
        }
      });
    };

    return {
      modalState,
      oauthSetupState,
      configuredServices,
      showConfigModal,
      closeConfigModal,
      closeOAuthSetup,
      handleOAuthComplete,
      submitConfiguration
    };
  };

  // Use external service configuration hook
  const {
    modalState,
    oauthSetupState,
    configuredServices,
    showConfigModal,
    closeConfigModal,
    closeOAuthSetup,
    handleOAuthComplete,
    submitConfiguration
  } = useExternalServiceConfig(wsConnection, userId);

  const ExternalActionButton = ({ externalActionRequest, parentMessageId, onConfigureService }) => {
    const isConfigured = configuredServices.has(externalActionRequest.service_name);

    const handleClick = (e) => {
      e.preventDefault();
      e.stopPropagation();
      if (!isConfigured) {
        onConfigureService(externalActionRequest, parentMessageId);
      }
    };

    return (
      <div className="mt-3 pt-2 border-t border-gray-200">
        <button
          onClick={handleClick}
          disabled={isConfigured}
          className={`external-action-button inline-flex items-center gap-2 px-4 py-2 font-normal font-['Hind'] rounded-lg transition-colors duration-200 min-h-[40px] min-w-[140px] focus:outline-none focus:ring-2 shadow-sm ml-4 mb-4 ${
            isConfigured
              ? 'bg-green-600 text-white cursor-default shadow-md'
              : 'bg-primary text-white hover:bg-primary-600 active:bg-primary-700 focus:ring-primary-500 focus:ring-offset-2 hover:shadow-md cursor-pointer'
          }`}
        >
          {isConfigured ? (
            <>
              <Check className="w-4 h-4" />
              <span>Configured</span>
            </>
          ) : (
            <>
              <Settings className="w-4 h-4" />
              <span>Configure {externalActionRequest.service_name || 'Service'}</span>
            </>
          )}
        </button>
      </div>
    );
  };
  // EXTERNAL SERVICE MODAL COMPONENT - Orange Theme
  const ExternalServiceModal = ({ 
    isOpen, 
    onClose, 
    externalActionRequest, 
    parentMessageId, 
    onSubmit 
  }) => {
    const [formData, setFormData] = useState({});
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [errors, setErrors] = useState({});
    const [inputStates, setInputStates] = useState({});
  
    // Generate a unique key for localStorage based on service and parent message
    const getStorageKey = useCallback(() => {
      if (externalActionRequest?.service_name && parentMessageId) {
        return `external-service-form-${externalActionRequest.service_name}-${parentMessageId}`;
      }
      return null;
    }, [externalActionRequest?.service_name, parentMessageId]);

    // Load form data from localStorage or initialize with empty values
    useEffect(() => {
      if (isOpen && externalActionRequest?.environment_variable) {
        const storageKey = getStorageKey();
        
        // Try to load existing form data from localStorage
        let savedFormData = {};
        if (storageKey) {
          try {
            const savedData = localStorage.getItem(storageKey);
            if (savedData) {
              savedFormData = JSON.parse(savedData);
            }
          } catch (error) {
            console.warn('Failed to load saved form data:', error);
          }
        }
        
        // Initialize form data with saved values or empty strings
        const initialData = {};
        const initialInputStates = {};
        externalActionRequest.environment_variable.forEach(envVar => {
          initialData[envVar.field_name] = savedFormData[envVar.field_name] || '';
          initialInputStates[envVar.field_name] = { isFocused: false };
        });
        
        setFormData(initialData);
        setInputStates(initialInputStates);
        setErrors({});
      }
    }, [isOpen, externalActionRequest, getStorageKey]);

    // Save form data to localStorage whenever it changes
    useEffect(() => {
      const storageKey = getStorageKey();
      if (storageKey && Object.keys(formData).length > 0) {
        try {
          localStorage.setItem(storageKey, JSON.stringify(formData));
        } catch (error) {
          console.warn('Failed to save form data:', error);
        }
      }
    }, [formData, getStorageKey]);

    // Clear localStorage when modal is closed or form is submitted successfully
    const clearStoredFormData = useCallback(() => {
      const storageKey = getStorageKey();
      if (storageKey) {
        try {
          localStorage.removeItem(storageKey);
        } catch (error) {
          console.warn('Failed to clear stored form data:', error);
        }
      }
    }, [getStorageKey]);
  
    const validateForm = () => {
      const newErrors = {};
      
      if (externalActionRequest?.environment_variable) {
        externalActionRequest.environment_variable.forEach(envVar => {
          const value = formData[envVar.field_name];
          if (!value || !value.trim()) {
            newErrors[envVar.field_name] = `${envVar.display_name || envVar.field_name} is required`;
          }
        });
      }
      
      setErrors(newErrors);
      return Object.keys(newErrors).length === 0;
    };
  
    const handleInputChange = (fieldName, value) => {
      setFormData(prev => ({
        ...prev,
        [fieldName]: value
      }));
      
      if (errors[fieldName]) {
        setErrors(prev => ({
          ...prev,
          [fieldName]: undefined
        }));
      }
    };

    const handleModalInputFocus = (fieldName) => {
      setInputStates(prev => ({
        ...prev,
        [fieldName]: { ...prev[fieldName], isFocused: true }
      }));
    };

    const handleModalInputBlur = (fieldName) => {
      setInputStates(prev => ({
        ...prev,
        [fieldName]: { ...prev[fieldName], isFocused: false }
      }));
    };

    const handleClose = () => {
      clearStoredFormData();
      onClose();
    };
  
    const handleSubmit = async () => {
      if (!validateForm()) return;
  
      setIsSubmitting(true);
      
      try {
        const configData = {};
        Object.keys(formData).forEach(key => {
          configData[key] = formData[key].trim();
        });
  
        const extra = {
          type: "setup_service",
          service_name: externalActionRequest.service_name,
          action_type: externalActionRequest.action_type,
          ...configData,
          vars: Object.keys(configData).map(key => ({
            name: key,
            value: configData[key]
          }))
        };
  
        const responseContent = `✅ Service configuration submitted for ${externalActionRequest.service_name}`;
        
        await onSubmit(responseContent, [], extra, parentMessageId);
        
        // Clear stored data on successful submission
        clearStoredFormData();
        
        setTimeout(() => {
          onClose();
        }, 1500);
        
      } catch (error) {
        console.error('Error submitting service configuration:', error);
        setErrors({ submit: 'Failed to submit configuration. Please try again.' });
      } finally {
        setIsSubmitting(false);
      }
    };
  
    const handleKeyDown = (e) => {
      if (e.key === 'Escape') {
        handleClose();
      }
      if (e.key === 'Enter' && !e.shiftKey) {
        e.preventDefault();
        handleSubmit();
      }
    };
  
    const getInputType = (fieldName) => {
      const sensitiveFields = ['password', 'key', 'token', 'secret'];
      return sensitiveFields.some(field => fieldName.toLowerCase().includes(field)) ? 'password' : 'text';
    };
  
    if (!isOpen) return null;
  
    return (
      <div 
        className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
        onClick={(e) => e.target === e.currentTarget && handleClose()}
        onKeyDown={handleKeyDown}
        tabIndex={-1}
      >
        <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4 max-h-[90vh] overflow-y-auto">
          {/* Header with orange accent */}
          <div className="flex items-center justify-between p-6 border-b border-primary-100 bg-primary-50">
            <h3 className="text-lg font-semibold text-gray-900">
              Configure {externalActionRequest?.service_name || 'Service'}
            </h3>
            <button
              onClick={handleClose}
              className="text-gray-400 hover:text-primary-600 transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 rounded-lg p-1"
              disabled={isSubmitting}
            >
              <X className="w-6 h-6" />
            </button>
          </div>
  
          <div className="p-6">
            {/* Instructions with orange theme */}
            {externalActionRequest?.instructions && (
              <div className="mb-4 p-3 bg-primary-50 rounded-lg border border-primary-200">
                <p className="text-sm text-primary-800">
                  {externalActionRequest.instructions}
                </p>
              </div>
            )}
  
            {/* Help link with orange theme */}
            {externalActionRequest?.help_url && (
              <div className="mb-4">
                <a
                  href={externalActionRequest.help_url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center gap-1 text-primary-600 hover:text-primary-800 text-sm transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 rounded-lg px-1"
                >
                  <ExternalLink className="w-4 h-4" />
                  View documentation
                </a>
              </div>
            )}
  
            <div className="space-y-4">
              {externalActionRequest?.environment_variable?.map((envVar) => (
                <div key={envVar.field_name} className="space-y-1">
                  <label className="block text-sm font-medium text-gray-700">
                    {envVar.display_name || envVar.field_name}
                  </label>
                  <input
                    type={getInputType(envVar.field_name)}
                    value={formData[envVar.field_name] || ''}
                    onChange={(e) => handleInputChange(envVar.field_name, e.target.value)}
                    onFocus={() => handleModalInputFocus(envVar.field_name)}
                    onBlur={() => handleModalInputBlur(envVar.field_name)}
                    onKeyDown={handleKeyDown}
                    className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 transition-colors ${
                      errors[envVar.field_name] 
                        ? 'border-red-300 focus:ring-red-500' 
                        : inputStates[envVar.field_name]?.isFocused
                        ? 'border-primary-400 focus:border-primary-500'
                        : 'border-gray-300 focus:border-primary'
                    }`}
                    placeholder={envVar.instructions || `Enter ${envVar.display_name || envVar.field_name}`}
                    disabled={isSubmitting}
                  />
                  {errors[envVar.field_name] && (
                    <p className="text-xs text-red-600 mt-1">
                      {errors[envVar.field_name]}
                    </p>
                  )}
                  {envVar.instructions && envVar.instructions !== `Enter ${envVar.display_name || envVar.field_name}` && (
                    <p className="text-xs text-gray-500 mt-1">
                      {envVar.instructions}
                    </p>
                  )}
                </div>
              ))}
  
              {/* Submit error with orange theme */}
              {errors.submit && (
                <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
                  <p className="text-sm text-red-800">{errors.submit}</p>
                </div>
              )}
  
              {/* Footer with orange theme */}
              <div className="flex justify-end gap-3 pt-4">
                <button
                  onClick={handleClose}
                  className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
                  disabled={isSubmitting}
                >
                  Cancel
                </button>
                <button
                  onClick={handleSubmit}
                  disabled={isSubmitting}
                  className={`px-4 py-2 rounded-lg font-medium transition-all focus:outline-none focus:ring-2 focus:ring-offset-2 ${
                    isSubmitting
                      ? 'bg-green-500 text-white cursor-not-allowed focus:ring-green-500'
                      : 'bg-primary text-white hover:bg-primary-600 focus:ring-primary-500'
                  }`}
                >
                  {isSubmitting ? (
                    <span className="flex items-center gap-2">
                      <Check className="w-4 h-4" />
                      Configured!
                    </span>
                  ) : (
                    'Configure Service'
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };
  const getDisplayName = useCallback((name, email) => {
    if (name) return name;
    if (email) {
      // Extract name from email by taking everything before the @ symbol
      return email.split('@')[0];
    }
    return 'User'; // Fallback if both name and email are null
  }, []);

  // USE EXTRACTED HOOKS
  const scrollHook = useScrollManagement(messagesContainerRef, setAutoScroll, setHasNewMessages);
  const { 
    isAtBottom, 
    scrollToBottom, 
    directScrollToBottom, 
    scrollToMessage, 
    handleScroll, 
    userManuallyScrolledRef, 
    isScrollingRef,
    isManualScrollingStillActive 
  } = scrollHook;

  const fileOpsHook = useFileOperations(messages, autoScroll, messagesContainerRef, isAtBottom);
  const {
    fileOperationsMap,
    pendingFileOperations,
    fileOpsUpdateCounter,
    updateFileOperations,
    processMessageFileUpdates
  } = fileOpsHook;

  // Process file_updates in existing messages
  useEffect(() => {
    if (messages.length > 0) {
      messages.forEach(message => {
        if (message && message.file_updates) {
          processMessageFileUpdates(message);
        }
      });
    }
  }, [messages, processMessageFileUpdates]);

  const rollbackHook = useRollback(wsConnection, searchParams);
  const {
    showRollbackModal,
    rollbackMessageId,
    isRollbackInProgress,
    handleRollbackClick,
    handleRollbackCancel,
    handleRollbackConfirm,
    handleRollbackStatusUpdate
  } = rollbackHook;

  // Update messages when initialCtxMessages changes with priority handling
  useEffect(() => {
    if (messages.length === 0 && initialCtxMessages.length > 0) {
      setIsLoadingHistoricalMessages(true);
      
      // Sort messages by timestamp to ensure proper order
      const sortedMessages = [...initialCtxMessages].sort((a, b) =>
        new Date(a.timestamp) - new Date(b.timestamp)
      );
      
      setMessages(sortedMessages);
      historicalMessagesLoadedRef.current = true;
      setIsLoadingHistoricalMessages(false);
      
      // Handle message resolution state from historical messages
      const hasPendingMessages = sortedMessages.some(msg =>
        msg.status === 'pending' && msg.requires_resolution === true
      );
      
      if (hasPendingMessages) {
        // Find the message that needs resolution
        const pendingMessage = sortedMessages.find(msg =>
          msg.status === 'pending' && msg.requires_resolution === true
        );
        
        if (pendingMessage) {
          
          setActiveReplyTo(pendingMessage.id);
          setIsInputEnabled(true);
          
          // Clear AI typing state since we have a message waiting for user response
          setIsAiTyping(false);
        }
      } else {
        // No pending messages, ensure input is enabled for new conversations
        setIsInputEnabled(true);
        setIsAiTyping(false);
      }
      
      // Process any queued socket messages after historical messages are loaded
      setTimeout(() => {
        if (socketMessageQueueRef.current.length > 0) {
          const queuedMessages = [...socketMessageQueueRef.current];
          socketMessageQueueRef.current = [];
          
          queuedMessages.forEach(({ message, messageType }) => {
            safeAddOrUpdateMessageInState(message, messageType);
          });
        }
      }, 100);
    }
  }, [initialCtxMessages, messages.length, setActiveReplyTo, setIsInputEnabled, setIsAiTyping]);

  // UTILITY FUNCTIONS
  const hasMessagesNeedingResponse = useCallback(() => {
    return messages.some(msg =>
      (msg.status === 'needs_response' || msg.status === 'pending') &&
      (msg.msg_type === 'llm' || msg.msg_type === 'error')
    );
  }, [messages]);

  const setFirstMessageNeedingResponseAsActive = useCallback(() => {
    const needsResponse = messages.find(msg =>
      (msg.status === 'needs_response' || msg.status === 'pending') &&
      (msg.msg_type === 'llm' || msg.msg_type === 'error')
    );

    if (needsResponse) {
      setActiveReplyTo(needsResponse.id);
      setIsInputEnabled(true);
    }
  }, [messages, setActiveReplyTo]);

  // Handle input/reply state management
  useEffect(() => {
    if (hasMessagesNeedingResponse() && !activeReplyTo) {
      setFirstMessageNeedingResponseAsActive();
    } else if (!hasMessagesNeedingResponse() && !activeReplyTo) {
      setIsInputEnabled(true);
    }

    if (activeReplyTo) {
      const activeMessage = messages.find(msg => msg.id === activeReplyTo);
      if (!activeMessage ||
        !(activeMessage.status === 'needs_response' || activeMessage.status === 'pending')) {
        setActiveReplyTo(null);
        setIsInputEnabled(true);
      }
    }
  }, [messages, activeReplyTo, hasMessagesNeedingResponse, setFirstMessageNeedingResponseAsActive, setActiveReplyTo]);

  // Handle new messages arriving
  useEffect(() => {
    if (messages.length > lastMessageCountRef.current) {
      lastMessageCountRef.current = messages.length;

      if (autoScroll && isAtBottom()) {
        setTimeout(() => scrollToBottom(), 50);
      } else if (!autoScroll || !isAtBottom()) {
        setHasNewMessages(true);
      }
    }
  }, [messages.length, autoScroll, isAtBottom, scrollToBottom, setHasNewMessages]);

  // ADD OR UPDATE MESSAGE (Prevents duplicates)
  const addOrUpdateMessageInState = useCallback((incomingMessage, messageType) => {
    setMessages((prev) => {
      // Special handling for deployment status messages
      if (incomingMessage.type === "deployment_status" && incomingMessage.deployment_data?.id) {
        const deploymentId = incomingMessage.deployment_data.id;
        const existingDeploymentMsgIndex = prev.findIndex(
          m => m.type === "deployment_status" && m.deployment_data?.id === deploymentId
        );

        if (existingDeploymentMsgIndex >= 0) {
          const updatedList = [...prev];
          const oldMessage = updatedList[existingDeploymentMsgIndex];
          updatedList[existingDeploymentMsgIndex] = {
            ...oldMessage,
            ...incomingMessage,
            deployment_data: incomingMessage.deployment_data,
            content: incomingMessage.content,
            timestamp: incomingMessage.timestamp || oldMessage.timestamp
          };
          return updatedList;
        }
      }

      // Regular message handling
      const existingMsgIndex = prev.findIndex((m) => m.id === incomingMessage.id);
      const isNewMessage = existingMsgIndex === -1;

      let updatedList;
      if (isNewMessage) {
        if (incomingMessage.msg_type === 'system' && incomingMessage.type !== 'deployment_status') {
          return prev;
        }
        updatedList = [...prev, { ...incomingMessage }];
      } else {
        updatedList = [...prev];
        const oldMessage = updatedList[existingMsgIndex];

        let newContent;
        if (messageType === 'agent_message_streaming') {
          newContent = incomingMessage.content;
        } else if (incomingMessage.status === 'streaming') {
          // Handle streaming content with overlap detection
          if (oldMessage.content && incomingMessage.content) {
            if (oldMessage.content.includes(incomingMessage.content)) {
              newContent = oldMessage.content;
            } else if (incomingMessage.content.includes(oldMessage.content)) {
              newContent = incomingMessage.content;
            } else {
              // Find overlap to prevent duplication
              let maxOverlap = 0;
              const oldContentLength = oldMessage.content.length;
              const newContentLength = incomingMessage.content.length;

              for (let i = 1; i < Math.min(oldContentLength, newContentLength); i++) {
                const oldContentSuffix = oldMessage.content.slice(oldContentLength - i);
                const newContentPrefix = incomingMessage.content.slice(0, i);

                if (oldContentSuffix === newContentPrefix) {
                  maxOverlap = i;
                }
              }

              if (maxOverlap > 0) {
                newContent = oldMessage.content + incomingMessage.content.slice(maxOverlap);
              } else {
                newContent = oldMessage.content + incomingMessage.content;
              }
            }
          } else {
            newContent = (oldMessage.content || '') + (incomingMessage.content || '');
          }
        } else {
          newContent = incomingMessage.content;
        }

        const updatedUserDetails = incomingMessage.user_details || oldMessage.user_details;

        updatedList[existingMsgIndex] = {
          ...oldMessage,
          ...incomingMessage,
          content: newContent,
          user_details: updatedUserDetails
        };
        
      }

      return updatedList;
    });

    // Handle message status and reply state with error handling
    try {
      if (activeReplyTo === incomingMessage.id) {
        if (!(incomingMessage.status === 'needs_response' || incomingMessage.status === 'pending')) {
          setActiveReplyTo(null);
          setIsInputEnabled(false);
        }
      }

      if ((incomingMessage.status === 'needs_response' || incomingMessage.status === 'pending') &&
        (incomingMessage.msg_type === 'llm' || incomingMessage.msg_type === 'error')) {
        setActiveReplyTo(incomingMessage.id);
        setIsInputEnabled(true);
      }

      if (messageType === 'message_added') {
        setTimeout(() => {
          try {
            scrollToMessage(incomingMessage.id);
          } catch (scrollError) {
            console.error('Error scrolling to message:', scrollError);
          }
        }, 100);
      }
    } catch (stateError) {
      console.error('Error updating message state:', stateError);
    }
  }, [activeReplyTo, setActiveReplyTo, scrollToMessage]);

  // Batched message updates for streaming
  const [updateBatch, setUpdateBatch] = useState([]);
  const updateBatchTimeoutRef = useRef(null);

  const safeAddOrUpdateMessageInState = useCallback((incomingMessage, messageType) => {
    try {
      if (!incomingMessage || typeof incomingMessage !== 'object') {
        console.warn('Invalid message received:', incomingMessage);
        return;
      }

      // Generate a more robust message ID if missing
      const messageId = incomingMessage.id || `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      let safeMessage = { ...incomingMessage, id: messageId };

      // Ensure required fields are present
      if (!safeMessage.timestamp) {
        safeMessage.timestamp = new Date().toISOString();
      }

      // Validate and handle different message types
      if (safeMessage.msg_type === 'user') {
        // Ensure user_details structure exists for proper rendering
        if (!safeMessage.user_details) {
          safeMessage.user_details = {
            name: name || email?.split('@')[0] || 'User',
            email: email,
            user_id: userId
          };
        }
        
        // Ensure sender is set
        if (!safeMessage.sender) {
          safeMessage.sender = safeMessage.user_details.name || 'User';
        }
      } else if (safeMessage.msg_type === 'llm') {
        // Ensure LLM messages have proper structure
        if (!safeMessage.sender) {
          safeMessage.sender = 'Assistant';
        }
        
        // Handle messages that need response
        if (safeMessage.status === 'needs_response' || safeMessage.requires_resolution) {
          // Ensure content is properly formatted
          if (!safeMessage.content || typeof safeMessage.content !== 'string') {
            console.warn('LLM message with needs_response status has invalid content:', safeMessage);
            safeMessage.content = safeMessage.content || 'Message content unavailable';
          }
        }
      }

      // Ensure content is always a string
      if (safeMessage.content !== undefined && typeof safeMessage.content !== 'string') {
        safeMessage.content = String(safeMessage.content);
      }

      // NEVER queue user messages - always process them immediately
      const isUserMessage = safeMessage.msg_type === 'user' ||
                           messageType === 'message_received' ||
                           messageType === 'input_received' ||
                           messageType === 'user_message';

      if (isUserMessage) {
        addOrUpdateMessageInState(safeMessage, messageType);
        return;
      }

      // Priority handling: queue other socket messages if needed
      const shouldQueue = (isLoadingHistoricalMessages || (!historicalMessagesLoadedRef.current && messageType !== 'initial_messages'));
      
      if (shouldQueue) {
        // Queue socket messages until historical messages are loaded
        socketMessageQueueRef.current.push({ message: safeMessage, messageType });
        return;
      }

      // For streaming messages, use batching
      if (incomingMessage.status === 'streaming' || messageType === 'message_chunk') {
        setUpdateBatch(prev => [...prev, { message: safeMessage, type: messageType }]);

        if (updateBatchTimeoutRef.current) {
          clearTimeout(updateBatchTimeoutRef.current);
        }

        updateBatchTimeoutRef.current = setTimeout(() => {
          setUpdateBatch(current => {
            if (current.length > 0) {
              const messageMap = new Map();

              current.forEach(({ message, type }) => {
                if (!messageMap.has(message.id)) {
                  messageMap.set(message.id, { message: { ...message, content: message.content || '' }, type });
                } else {
                  const existing = messageMap.get(message.id);

                  if (existing.message.status === 'streaming' && message.status === 'streaming') {
                    const existingContent = existing.message.content || '';
                    const newContent = message.content || '';

                    if (!existingContent.includes(newContent) && !newContent.includes(existingContent)) {
                      let maxOverlap = 0;
                      for (let i = 1; i < Math.min(existingContent.length, newContent.length); i++) {
                        const oldSuffix = existingContent.slice(existingContent.length - i);
                        const newPrefix = newContent.slice(0, i);
                        if (oldSuffix === newPrefix) {
                          maxOverlap = i;
                        }
                      }

                      if (maxOverlap > 0) {
                        existing.message.content = existingContent + newContent.slice(maxOverlap);
                      } else {
                        existing.message.content = existingContent + newContent;
                      }
                    } else if (newContent.length > existingContent.length && newContent.includes(existingContent)) {
                      existing.message.content = newContent;
                    }
                  } else {
                    messageMap.set(message.id, { message, type });
                  }
                }
              });

              Array.from(messageMap.values()).forEach(({ message, type }) => {
                addOrUpdateMessageInState(message, type);
              });
            }

            return [];
          });

          if (autoScroll && isAtBottom()) {
            requestAnimationFrame(() => {
              if (messagesContainerRef.current) {
                messagesContainerRef.current.scrollTop = messagesContainerRef.current.scrollHeight;
              }
            });
          } else if (!autoScroll && !hasNewMessages) {
            setHasNewMessages(true);
          }
        }, 150);
      } else {
        addOrUpdateMessageInState(safeMessage, messageType);

        if (autoScroll && isAtBottom()) {
          requestAnimationFrame(() => {
            if (messagesContainerRef.current) {
              messagesContainerRef.current.scrollTop = messagesContainerRef.current.scrollHeight;
            }
          });
        } else if (!autoScroll && messageType === 'message_received') {
          setHasNewMessages(true);
        }
      }
    } catch (error) {
      console.error('Error in safeAddOrUpdateMessageInState:', error);
    }
  }, [addOrUpdateMessageInState, autoScroll, messagesContainerRef, isAtBottom, hasNewMessages, setHasNewMessages, isLoadingHistoricalMessages, name, email, userId]);

  // Handle initial messages with priority handling
  const handleInitialMessages = useCallback((msgs) => {
    setIsLoadingHistoricalMessages(true);
    
    const latestMessages = new Map();

    msgs
      .slice()
      .reverse()
      .filter(m => m.msg_type !== 'system' || m.type === 'deployment_status')
      .forEach(msg => {
        if (!latestMessages.has(msg.id)) {
          latestMessages.set(msg.id, msg);
        }
      });

    const sortedMessages = Array.from(latestMessages.values())
      .reverse()
      .sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));

    // Mark historical messages as loaded first
    historicalMessagesLoadedRef.current = true;
    
    // Process historical messages
    sortedMessages.forEach(msg => safeAddOrUpdateMessageInState(msg, 'initial_messages'));
    
    setIsLoadingHistoricalMessages(false);
    
    // Process any queued socket messages after a brief delay
    setTimeout(() => {
      if (socketMessageQueueRef.current.length > 0) {
        const queuedMessages = [...socketMessageQueueRef.current];
        socketMessageQueueRef.current = [];
        
        queuedMessages.forEach(({ message, messageType }) => {
          safeAddOrUpdateMessageInState(message, messageType);
        });
      }
    }, 50);

  }, [safeAddOrUpdateMessageInState]);

  // Initialize autoScroll
  useEffect(() => {
    setAutoScroll(true);
    
    const scrollAttempts = [0, 100, 500];
    scrollAttempts.forEach(delay => {
      setTimeout(() => {
        if (messagesContainerRef.current) {
          messagesContainerRef.current.scrollTop = messagesContainerRef.current.scrollHeight;
        }
      }, delay);
    });
  }, [setAutoScroll, messagesContainerRef]);

  // Auto-scroll on content changes
  useEffect(() => {
    if (!messagesContainerRef.current || typeof MutationObserver === 'undefined') return;

    const observer = new MutationObserver(() => {
      if (autoScroll && !isScrollingRef.current && isAtBottom()) {
        scrollToBottom();
      }
    });

    observer.observe(messagesContainerRef.current, {
      childList: true,
      subtree: true
    });

    return () => observer.disconnect();
  }, [messagesContainerRef, autoScroll, scrollToBottom, isAtBottom, isScrollingRef]);

  // Handle deploymentStatus prop changes
  useEffect(() => {
    if (deploymentStatus && deploymentStatus.id) {
      const deploymentId = deploymentStatus.id;

      setDeploymentStatusMap(prevMap => ({
        ...prevMap,
        [deploymentId]: deploymentStatus
      }));

      let contentMessage = `🚀 Deployment Status: ${deploymentStatus.deployment_type || 'Application'} - ${deploymentStatus.status}`;
      if (deploymentStatus.app_url && deploymentStatus.status === 'success') {
        contentMessage += `\nApplication URL: ${deploymentStatus.app_url}`;
      }
      if (deploymentStatus.branch) {
        contentMessage += `\nBranch: ${deploymentStatus.branch}`;
      }

      const deploymentStatusMessage = {
        id: `deployment-${deploymentId}`,
        type: "deployment_status",
        content: contentMessage,
        sender: "System",
        deployment_data: deploymentStatus,
        timestamp: new Date().toISOString(),
        msg_type: "system_message"
      };

      setTimeout(() => {
        safeAddOrUpdateMessageInState(deploymentStatusMessage, 'deployment_status');
        setTimeout(() => {
          scrollToBottom();
        }, 200);
      }, 0);
    }
  }, [deploymentStatus, safeAddOrUpdateMessageInState, scrollToBottom]);

  // USE WEBSOCKET MESSAGES HOOK
  useWebSocketMessages({
    wsConnection,
    handleInitialMessages,
    safeAddOrUpdateMessageInState,
    autoScroll,
    isAtBottom,
    scrollToBottom,
    isManualScrollingStillActive,
    setIsAiTyping,
    setIsReady,
    updateFileOperations,
    setMessages,
    messages,
    fileOperationsMap,
    openAccordions,
    setOpenAccordions,
    pendingFileOperations,
    handleRollbackStatusUpdate,
    processMessageFileUpdates,
    isLoadingHistoricalMessages,
    historicalMessagesLoadedRef,
    fetchInitialMessages,
    taskId: searchParams.get("task_id")
  });

  // Handle sending messages
  const handleSendMessage = useCallback(() => {
    const text = inputValue.trim();
    const hasContent = text.length > 0 || attachments.length > 0;

    if (!hasContent) return;

    setAutoScroll(true);

    if (wsConnection?.readyState === WebSocket.OPEN) {
      const messagePayload = {
        type: 'send_message',
        content: text,
        parent_id: activeReplyTo,
        user_id: userId
      };

      if (uploadedAttachments.length > 0) {
        messagePayload.attachment_ids = uploadedAttachments.map(a => a.attachment_id);
      }

      if (attachments.length > 0) {
        messagePayload.attachments = attachments;
      }

      wsConnection.send(JSON.stringify(messagePayload));
    }

    setInputValue('');
    setAttachments([]);
    setAttachedFiles([]);
    setUploadedAttachments([]);

    if (textAreaRef.current) {
      textAreaRef.current.value = '';
      textAreaRef.current.style.height = '28px';
      textAreaRef.current.style.overflowY = 'hidden';
      textAreaRef.current.classList.remove('custom-scrollbar');
    }

    if (activeReplyTo) {
      setActiveReplyTo(null);
    }

    setTimeout(() => scrollToBottom(), 50);
  }, [
    inputValue,
    attachments,
    wsConnection,
    activeReplyTo,
    userId,
    uploadedAttachments,
    attachedFiles,
    textAreaRef,
    setActiveReplyTo,
    setAutoScroll,
    scrollToBottom
  ]);

    const handleExtendSession = async (messageId) => {
    if (wsConnection?.readyState === WebSocket.OPEN) {
      const message = {
        type: 'extend_session',
        action: 'extend',
        user_id: userId,
        timestamp: new Date().toISOString(),
        message_id: messageId
      };
      wsConnection.send(JSON.stringify(message));
    }
  };

  const handleEndSession = async () => {
    
  };

  // Optimize the chat rendering by memoizing messages more effectively
  const renderMessages = useMemo(() => {
    try {
      const displayName = getDisplayName(name, email);

      // Ensure messages are properly sorted by timestamp
      const filteredMessages = messages.filter(m => {
        try {
          // Show all user messages at top level (they represent conversation flow)
          if (m.msg_type === 'user') {
            return true;
          }
          
          // For non-user messages, apply the original filtering logic
          return !m.parent_id && (m.msg_type !== "system" && m.msg_type !== "SYSTEM" || m.type === "deployment_status");
        } catch (filterError) {
          console.error('Error filtering message:', filterError, m);
          return false;
        }
      });

      const sortedMessages = filteredMessages.sort((a, b) => {
        try {
          const timestampA = new Date(a.timestamp);
          const timestampB = new Date(b.timestamp);
          
          // Handle invalid dates
          if (isNaN(timestampA) || isNaN(timestampB)) {
            return 0;
          }
          
          return timestampA - timestampB;
        } catch (sortError) {
          console.error('Error sorting messages:', sortError);
          return 0;
        }
      });

      return sortedMessages.map(message => {
        try {
          // Validate message before rendering
          if (!message || !message.id) {
            console.warn('Invalid message skipped:', message);
            return null;
          }

          return (
            <div key={`${message.id}-${fileOpsUpdateCounter}`}>

              <MessageElement
                key={`me${message.id}-${fileOpsUpdateCounter}`}
                message={message}
                messages={sortedMessages}
                activeReplyTo={activeReplyTo}
                setActiveReplyTo={setActiveReplyTo}
                setIsInputEnabled={setIsInputEnabled}
                textAreaRef={textAreaRef}
                scrollToMessage={scrollToMessage}
                isPanelExpanded={isPanelExpanded}
                getUserAvatar={() => getUserAvatar(displayName)}
                getKaviaAvatar={getKaviaAvatar}
                userId={userId}
                formatDateTime={formatDateTime}
                renderHTML={renderHTML}
                deploymentStatusMap={deploymentStatusMap}
                fileOperationsMap={fileOperationsMap}
                fileOpsUpdateCounter={fileOpsUpdateCounter}
                openAccordions={openAccordions}
                setOpenAccordions={setOpenAccordions}
                handleRollbackClick={handleRollbackClick}
                showConfigModal={showConfigModal}
                ExternalActionButton={ExternalActionButton}
              />
              
              {/* External action button rendered separately */}
              {message.extra?.action_request_to_user && (
                <ExternalActionButton
                  externalActionRequest={message.extra.action_request_to_user}
                  parentMessageId={message.id}
                  onConfigureService={showConfigModal}
                />
              )}

              {/* Cost Warning Element */}
              {
                message.extra?.type=="cost_warning" && (
                  <CostWarningElement
                      limit={message.extra.limit}
                      messageId={message.id}
                      onYes={handleExtendSession}
                      onNo={handleEndSession}
                  />
                )
              }
            </div>
          );
        } catch (renderError) {
          console.error('Error rendering message:', renderError, message);
          // Return a fallback message element
          return (
            <div key={`error-${message.id || Date.now()}`} className="p-4 bg-red-50 border border-red-200 rounded-lg">
              <p className="text-red-600">Error rendering message</p>
            </div>
          );
        }
      }).filter(Boolean); // Remove null entries
    } catch (error) {
      console.error('Error in renderMessages:', error);
      return (
        <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
          <p className="text-red-600">Error rendering messages. Please refresh the page.</p>
        </div>
      );
    }
  }, [
    messages,
    activeReplyTo,
    fileOperationsMap,
    fileOpsUpdateCounter,
    isPanelExpanded,
    setActiveReplyTo,
    setIsInputEnabled,
    openAccordions,
    setOpenAccordions,
    handleRollbackClick,
    scrollToMessage,
    getUserAvatar,
    getKaviaAvatar,
    userId,
    formatDateTime,
    renderHTML,
    deploymentStatusMap,
    textAreaRef,
    name,
    email,
    showConfigModal
  ]);

  // Auto-scroll when messages change
  useEffect(() => {
    if (messages.length > 0 && autoScroll && isAtBottom()) {
      requestAnimationFrame(() => {
        if (messagesContainerRef.current) {
          messagesContainerRef.current.scrollTop = messagesContainerRef.current.scrollHeight;
        }
      });
    }
  }, [messages, autoScroll, isAtBottom, messagesContainerRef]);

  // Focus event handlers
  const handleInputFocus = useCallback(() => {
    setInputFocused(true);
  }, []);

  const handleInputBlur = useCallback(() => {
    setInputFocused(false);
  }, []);

  // Comprehensive component cleanup
  useEffect(() => {
    return () => {
      isComponentMountedRef.current = false;
    };
  }, []);

  // Handle processing state - clear when we have meaningful messages
  useEffect(() => {
    if (messages.length > 0 && !isLoadingHistoricalMessages) {
      // Check if we have actual content messages (not just system messages)
      const hasContentMessages = messages.some(msg =>
        msg.msg_type === 'llm' ||
        msg.msg_type === 'user' ||
        (msg.msg_type === 'system' && msg.content && !msg.content.includes('Processing'))
      );
      
      if (hasContentMessages) {
        // Processing should be complete when we have meaningful content
      }
    }
  }, [messages, isLoadingHistoricalMessages]);

  // RENDER
  return (
    <>
      <div className="flex flex-col h-screen overflow-hidden">
        {!messages.some(msg => msg.msg_type === "llm") ? (
          taskStatus.toLowerCase() === "failed" ? (
            <ChatLoader message={"Failed to prepare environment. Please try again."} />
          ) : (
            <ChatLoader message={
              messages.filter(msg => msg.msg_type?.toLowerCase() === "system").length > 0
                ? messages.filter(msg => msg.msg_type?.toLowerCase() === "system")
                    .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))[0].content
                : "Preparing environment..."
            } />
          )
        ) : (
          <>
            {/* Messages container */}
            <div
              ref={messagesContainerRef}
              className={`flex-1 overflow-y-auto p-4 pl-0 custom-scrollbar messages-container relative`}
              onScroll={(e) => handleScroll(e, autoScroll)}
              style={{ scrollBehavior: 'auto', position: 'relative' }}
            >
              <div className="messages-wrapper">
                {renderMessages}
              </div>
            </div>

            {/* AI Typing indicator */}
            {/* {isAiTyping && <ThreeDotLoader />} */}

            {/* Rollback Progress Bar */}
            {isRollbackInProgress && <RollbackProgressBar />}

            {/* New messages indicator */}
            {/* <NewMessageIndicator 
              hasNewMessages={hasNewMessages}
              messagesContainerRef={messagesContainerRef}
              setAutoScroll={setAutoScroll}
              setHasNewMessages={setHasNewMessages}
            /> */}

            {/* Chat input */}
            <div className="w-full pt-1 pb-2 px-1.5 overflow-x-hidden">
              <ChatInput
                inputValue={inputValue}
                setInputValue={setInputValue}
                handleSendMessage={handleSendMessage}
                isReady={isReady}
                isAiTyping={isAiTyping}
                textAreaRef={textAreaRef}
                activeReplyTo={activeReplyTo}
                availableModels={propAvailableModels}
                isLoadingModels={propIsLoadingModels}
                wsConnection={wsConnection}
                attachedFiles={attachedFiles}
                setAttachedFiles={setAttachedFiles}
                uploadedAttachments={uploadedAttachments}
                setUploadedAttachments={setUploadedAttachments}
                isStopped={isStopped}
                onInputFocus={handleInputFocus}
                onInputBlur={handleInputBlur}
              />
            </div>
            <div className="text-xs text-gray-500 mt-1 ml-2">
              💡 Use @figma to tag and interact with Figma files
            </div>
          </>
        )}
      </div>

      {/* External Service Configuration Modal */}
      <ExternalServiceModal
        isOpen={modalState.isOpen}
        onClose={closeConfigModal}
        externalActionRequest={modalState.externalActionRequest}
        parentMessageId={modalState.parentMessageId}
        onSubmit={submitConfiguration}
      />

      {/* Supabase OAuth Setup Component */}
      {oauthSetupState.isOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="max-w-4xl w-full mx-4">
            <SupabaseOAuthChatSetup
              externalActionRequest={oauthSetupState.externalActionRequest}
              parentMessageId={oauthSetupState.parentMessageId}
              onComplete={handleOAuthComplete}
              onClose={closeOAuthSetup}
              projectId={searchParams.get('task_id') || 'default'}
            />
          </div>
        </div>
      )}

      {/* Rollback Confirmation Modal */}
      <ConfirmationModal
        showModal={showRollbackModal}
        functionCallData={{
          operation: "rollback",
          type: "Codebase Changes",
          details: {
            "Action": "Rollback to message point",
            "Message ID": rollbackMessageId || "",
            "Description": "This will stash all current changes in the codebase and rollback to the state before this message was processed."
          },
          task_id: searchParams.get("task_id")
        }}
        onCancel={handleRollbackCancel}
        onConfirm={handleRollbackConfirm}
      />
    </>
  );
};

export default ChatInterface;