import React, { useState } from 'react'
import PropTypes from 'prop-types'

const CostWarningElement = ({ limit, messageId, onYes, onNo }) => {
  const [isProcessing, setIsProcessing] = useState(false)
  const [hasResponded, setHasResponded] = useState(false)

  const handleYesClick = async () => {
    if (isProcessing || hasResponded) return
    
    setIsProcessing(true)
    setHasResponded(true)
    
    try {
      if (onYes) {
        await onYes(messageId)
      }
    } catch (error) {
      console.error('Error extending session:', error)
      setIsProcessing(false)
      setHasResponded(false)
    } finally {
      setIsProcessing(false)
    }
  }

  const handleNoClick = async () => {
    if (isProcessing || hasResponded) return
    
    setIsProcessing(true)
    setHasResponded(true)
    
    try {
      if (onNo) {
        await onNo()
      }
    } catch (error) {
      console.error('Error declining session extension:', error)
      setIsProcessing(false)
      setHasResponded(false)
    } finally {
      setIsProcessing(false)
    }
  }

  if (hasResponded) {
    return (
      <div className="bg-white border border-slate-200 rounded-xl p-4 mb-4 shadow-sm ml-7">
        <div className="flex items-center text-emerald-700">
          <div className="flex items-center space-x-3">
            <div className="flex-shrink-0">
              <div className="w-7 h-7 bg-gradient-to-br from-emerald-500 to-green-600 rounded-full flex items-center justify-center shadow-sm">
                <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
              </div>
            </div>
            <div>
              <p className="font-medium text-slate-800 text-sm">Response Recorded</p>
              <p className="text-xs text-slate-600">Your session preference has been saved</p>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white border border-slate-200/60 rounded-xl p-4 mb-4 shadow-lg shadow-slate-200/30 ml-7">
      <div className="flex items-start space-x-4">
        <div className="flex-shrink-0">
          <div className="w-9 h-9 bg-gradient-to-br from-amber-500 to-orange-600 rounded-xl flex items-center justify-center shadow-md">
            <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
          </div>
        </div>
        
        <div className="flex-1 min-w-0">
          <h3 className="text-base font-semibold text-slate-900 mb-1 tracking-tight">Session Limit Reached</h3>
          <p className="text-slate-600 leading-relaxed mb-4 text-sm">
            You've reached your <span className="font-semibold text-slate-800 bg-slate-100 px-2 py-0.5 rounded-md">${parseFloat(limit).toFixed(2)}</span> usage limit. 
            Would you like to extend your session?
          </p>
          
          <div className="flex gap-3">
            <button
              onClick={handleYesClick}
              disabled={isProcessing}
              className={`group px-4 py-2 rounded-lg font-medium text-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 ${
                isProcessing
                  ? 'bg-slate-100 text-slate-400 cursor-not-allowed'
                  : 'bg-gradient-to-r from-emerald-500 to-green-600 text-white hover:from-emerald-600 hover:to-green-700 focus:ring-emerald-500 shadow-md hover:shadow-lg transform hover:-translate-y-0.5 active:translate-y-0'
              }`}
            >
              <span className="flex items-center justify-center space-x-1">
                {isProcessing ? (
                  <>
                    <svg className="animate-spin h-3 w-3 text-slate-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    <span>Processing...</span>
                  </>
                ) : (
                  <>
                    <svg className="w-3 h-3 transition-transform group-hover:scale-110" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    <span>Yes, Continue</span>
                  </>
                )}
              </span>
            </button>
            
            <button
              onClick={handleNoClick}
              disabled={isProcessing}
              className={`group px-4 py-2 rounded-lg font-medium text-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 ${
                isProcessing
                  ? 'bg-slate-100 text-slate-400 cursor-not-allowed'
                  : 'bg-slate-50 text-slate-700 border border-slate-300 hover:border-red-300 hover:text-red-600 hover:bg-red-50 focus:ring-red-400 hover:shadow-md transform hover:-translate-y-0.5 active:translate-y-0'
              }`}
            >
              <span className="flex items-center justify-center space-x-1">
                {isProcessing ? (
                  <>
                    <svg className="animate-spin h-3 w-3 text-slate-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    <span>Processing...</span>
                  </>
                ) : (
                  <>
                    <svg className="w-3 h-3 transition-transform group-hover:scale-110" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                    </svg>
                    <span>No, End Session</span>
                  </>
                )}
              </span>
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

CostWarningElement.propTypes = {
  limit: PropTypes.number.isRequired,
  messageId: PropTypes.string.isRequired,
  onYes: PropTypes.func,
  onNo: PropTypes.func
}

export default CostWarningElement