'use client';

import { usePlanRestriction } from './PlanRestrictionContext';
import PremiumOverlay from '../PremiumOverlay';
import { useEffect, useState } from 'react';
import Cookies from 'js-cookie';
import { refreshAccessToken, removeCookie } from '../../utils/api';

const InterceptFetchWrapper = ({ children }) => {
    const { showPlanRestriction, setShowPlanRestriction, setCreditLimitCrossed } = usePlanRestriction();
    const [isUnauthorized, setIsUnauthorized] = useState(false);
    const [isCreditLimitExceeded, setIsCreditLimitExceeded] = useState(false);

    useEffect(() => {
        const originalFetch = window.fetch;

        window.fetch = async (url, options) => {
            try {
                const response = await originalFetch(url, options);

                // Handle authentication errors (401/403)
                if (response.status === 401 || response.status === 403) {
                    // Skip token refresh for auth endpoints to avoid infinite loops
                    if (!url.includes('/auth/') && !url.includes('/login')) {
                        const refreshToken = Cookies.get("refreshToken");
                        const tenant_id = Cookies.get("tenant_id");
                        
                        if (refreshToken && tenant_id) {
                            try {
                                console.log('401/403 detected, attempting token refresh...');
                                await refreshAccessToken(refreshToken, tenant_id);
                                console.log('Token refreshed, retrying original request...');
                                
                                // Retry the original request with new token
                                const updatedOptions = { ...options };
                                if (updatedOptions.headers) {
                                    const headers = new Headers(updatedOptions.headers);
                                    const newIdToken = Cookies.get("idToken");
                                    if (newIdToken) {
                                        headers.set('Authorization', `Bearer ${newIdToken}`);
                                        updatedOptions.headers = headers;
                                    }
                                }
                                
                                return await originalFetch(url, updatedOptions);
                            } catch (refreshError) {
                                console.error('Token refresh failed:', refreshError);
                                // Clean up tokens and let the error propagate
                                removeCookie("idToken");
                                removeCookie("refreshToken");
                                removeCookie("userId");
                                removeCookie("username");
                                removeCookie("email");
                            }
                        }
                    }
                }

                if (response.status === 402) {
                    // Clone the response to read the body
                    const clonedResponse = response.clone();
                    try {
                        const data = await clonedResponse.json();
                        
                        if (data.detail === "You are not authorized to access this resource.") {
                            setIsUnauthorized(true);
                            setIsCreditLimitExceeded(false);
                        } else if (data.detail === "Free Credits Used Up.") {
                            setIsUnauthorized(false);
                            setIsCreditLimitExceeded(true);
                            setCreditLimitCrossed(true);
                        } else {
                            // Default case
                            setIsCreditLimitExceeded(true);
                            setCreditLimitCrossed(true);
                        }
                        
                        setShowPlanRestriction(true);
                    } catch (parseError) {
                        
                        // Default behavior for unparseable responses
                        setIsCreditLimitExceeded(true);
                        setCreditLimitCrossed(true);
                        setShowPlanRestriction(true);
                    }
                }

                return response;
            } catch (error) {
                

                if (url.includes('/auth/') || url.includes('/login')) {
                    return new Response(JSON.stringify({
                        error: true,
                        message: error.message || 'Network error occurred'
                    }), {
                        status: 500,
                        headers: { 'Content-Type': 'application/json' }
                    });
                }
                throw error;
            }
        };

        return () => {
            window.fetch = originalFetch;
        };
    }, [setShowPlanRestriction, setCreditLimitCrossed]);

    const handleClose = () => {
        setShowPlanRestriction(false);
        setIsUnauthorized(false);
        setIsCreditLimitExceeded(false);
    };

    return (
        <>
            {children}
            {showPlanRestriction && (
                <PremiumOverlay 
                    isCreditLimitExceeded={isCreditLimitExceeded} 
                    isUnauthorized={isUnauthorized} 
                    onClose={handleClose} 
                    allowClose={true} 
                />
            )}
        </>
    );
};

export default InterceptFetchWrapper;
