"use client";

import React, { useEffect, useState, useContext, useCallback, useRef } from "react";
import { useRouter, usePathname, useSearchParams } from "next/navigation";
import { fetchSystemContextWithContainers, getReconfigNodeStatus, updateNodeByPriority } from "@/utils/api";
import en from "@/en.json";
import { ArchitectureContext } from "@/components/Context/ArchitectureContext";
import { AlertContext } from "@/components/NotificationAlertService/AlertList";
import EmptyStateView from "@/components/Modal/EmptyStateModal";
import Badge from "@/components/UIComponents/Badge/Badge";
import ErrorView from "@/components/Modal/ErrorViewModal";
import { IconButton } from '@/components/UIComponents/Buttons/IconButton';
import { ArrowLeft, CodeXml, Eye, FolderGit2 } from "lucide-react";
import GenericCardGrid from "@/components/UIComponents/GenericCards/GenericCardGrid";
import CardGroupSkeletonLoder from "@/components/UIComponents/Loaders/CardGroupSkeletonLoder";
import RequirementsBanner from "@/components/UIComponents/Badge/RequirementBanner";
import "@/styles/tabs/architecture/container.css";
import CodeGenerationSetupModal from "@/app/modal/CodeGenerationSetupModal";
import CodeGenerationModal from "@/app/modal/CodeGenerationModal";
import ManifestEditor from '@/app/modal/ManifestEditor';
import { useCodeGeneration } from "@/components/Context/CodeGenerationContext";
import { useWebSocket } from '@/components/Context/WebsocketContext';
import { PLATFORMS, frameworks, Generic, backendFrameworks, mobileFrameworks , databaseFrameworks } from "@/constants/code_gen/platforms";
import { BranchSelector } from "@/components/Git/BranchSelector";
import { getRepository, listAllBranches } from "@/utils/repositoryAPI";
import RepositoryDetailsModal from "@/components/Modal/RepositoryModal";
import PastTasksModal from "@/components/Modal/PastTasksModal";
import { getPastCodeGenerationTasks, getPastCodeTasks } from "@/utils/batchAPI";
import Sessions from "@/components/Sessions/Sessions";
import Pagination from "@/components/UIComponents/Paginations/Pagination";
import { transformSessionsResponse } from "@/utils/sessionUtils";
import { Info } from "lucide-react";
import dayjs from 'dayjs';
import { buildProjectUrl } from '@/utils/navigationHelpers';
import { fetchEventSource } from '@microsoft/fetch-event-source';
import Cookies from "js-cookie";
import { contain } from "svg-pan-zoom";
import { da } from "date-fns/locale";

const CodeGenerationInfo = () => {
  return (
    <div className="flex items-start gap-2 bg-primary-50 rounded-md p-2 border-l-2 border-primary-400 mb-4">
      <div className="text-primary-600 flex-shrink-0 mt-0.5">
        <Info size={14} />
      </div>
      <div>
        <h2 className="text-gray-800 font-weight-medium typography-body-sm">When to use Code Generation</h2>
        <i className="text-gray-600 mt-0.5 typography-caption leading-tight">
          Generate new code components based on design specifications. Select a container and start the process
        </i>
      </div>
    </div>
  );
};

const ContainerList = ({isModal=false}) => {
  const { showAlert } = useContext(AlertContext);
  const [loading, setLoading] = useState(true);
  const [containerList, setContainerList] = useState([]);
  const { setContainerData } = useContext(ArchitectureContext);
  const searchParams = useSearchParams();
  const pathname = usePathname();
  const projectId = pathname.split("/")[3];
  const router = useRouter();
  const [error, setError] = useState(null);
  const [showBaner, setShowBaner] = useState(false);
  const [reconfigCount, setReconfigCount] = useState(0);

  // Code generation state variables
  const [codeGenSetupModal, setCodeGenSetupModal] = useState(false);
  const [isGeneratingCode, setIsGeneratingCode] = useState(false);
  const [isInitiating, setIsInitiating] = useState(false);
  const [currentPlatform, setCurrentPlatform] = useState({ key: "generic", label: "Generic", icon: <Generic /> });
  const [currentFramework, setCurrentFramework] = useState(frameworks[0]);
  const [selectedContainer, setSelectedContainer] = useState({});
  const [selectedContainerId, setSelectedContainerId] = useState("");
  const { isVisible, setIsVisible, setIsCodeMaintenance, setCurrentIframeUrl } = useCodeGeneration();
  const { connectToSession, disconnectFromSession } = useWebSocket();
  const [logInfo, setLogInfo] = useState('');
  const [controller, setController] = useState(null);
  const [plannedTaskId, setPlannedTaskId] = useState(null);
  const [isCompleted, setIsCompleted] = useState(false);
  const hasGeneratedCode = useRef(false);

  // Multi-select state for code generation
  const [selectedContainers, setSelectedContainers] = useState({
    all_containers: true,
    containers: []
  });
  const [isAllSelected, setIsAllSelected] = useState(true);

  // New ManifestEditor state
  const [showManifestEditor, setShowManifestEditor] = useState(false);

  // Repository state variables
  const [showRepoDetails, setShowRepoDetails] = useState(false);
  const [isRepoConfigured, setIsRepoConfigured] = useState(false);
  const [repositoryState, setRepositoryState] = useState({
    state: 'initial',
    data: null
  });

  // Loading states for individual buttons
  const [loadingStates, setLoadingStates] = useState({});

  // Branch management state
  const [containerBranches, setContainerBranches] = useState({});

  // Past tasks state variables
  const [isPastTasksCodeModalOpen, setIsPastTasksCodeModalOpen] = useState(false);
  const [pastTasks, setPastTasks] = useState([]);
  const [totalCount, setTotalCount] = useState(0);
  const [limit, setLimit] = useState(10);
  const [skip, setSkip] = useState(0);
  const [isPastTaskLoading, setIsPastTaskLoading] = useState(false);

  // History modal state variables
  const [sessions, setSessions] = useState([]);
  const [isHistoryLoading, setIsHistoryLoading] = useState(true);
  const [isHistoryOpen, setIsHistoryOpen] = useState(false);
  const [historyPagination, setHistoryPagination] = useState({
    currentPage: 1,
    pageSize: 10,
    totalItems: 0
  });
  const [historyFilters, setHistoryFilters] = useState({
    search: '',
    type: null,
    status: null,
    created_at: null
  });

  // Function to proceed with code generation after manifest is saved
  const proceedWithCodeGeneration = async () => {
    setIsInitiating(true);
    setLogInfo('');
    setIsGeneratingCode(true);
    hasGeneratedCode.current = false;
    setIsCompleted(false);
    setIsCodeMaintenance(false);
    sessionStorage.setItem("isCodeMaintenance", "false");

    // Store selected containers
    let storedSelectedContainers = sessionStorage.getItem("selectedContainers");
    if (!storedSelectedContainers) {
      const filteredContainers = selectedContainers.all_containers
        ? containerList.data.containers.filter((container)=>{
          // internal container
          if (container.properties?.ContainerType?.toLowerCase() === "internal" || container.properties?.ContainerType === undefined) {
            return true;
          }
          return false;
        }).map(c =>c.id)
        : selectedContainers.containers.filter(id => id);
      sessionStorage.setItem("selectedContainers", JSON.stringify(filteredContainers));
    }

    const abortController = new AbortController();
    setController(abortController);

    try {
      if (!hasGeneratedCode.current) {
        hasGeneratedCode.current = true;

        let url = `${process.env.NEXT_PUBLIC_API_URL}/batch/start_code_generation/${projectId}/`;
        let filteredContainers = selectedContainers.all_containers
        ? containerList.data.containers.filter((container)=>{
          // internal container
          if (container.properties?.ContainerType?.toLowerCase() === "internal" || container.properties?.ContainerType === undefined) {
            return true
          }
          return false
        }).map(c => c.id)
        : selectedContainers.containers.filter(id => id);
        fetchEventSource(
          url,
          {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${Cookies.get('idToken')}`,
              'Content-Type': 'application/json',
              'X-Tenant-Id': Cookies.get('tenant_id'),
            },
            body: JSON.stringify({
              request_timestamp: new Date().toISOString(),
              container_ids: filteredContainers || containerList.data.containers.map(c => c.id),
            }),
            signal: abortController.signal,
            openWhenHidden: true,
            onopen: (response) => {
              if (response.status !== 200) {
                showAlert("Something went wrong!", "error");
                abortController.abort();
                setShowManifestEditor(false);
                confirmClose();
              }
              return Promise.resolve();
            },
            onmessage: (event) => {
              try {
                const data = JSON.parse(event.data);

                if (data.task_id) {
                  const newSearchParams = new URLSearchParams(searchParams);
                  newSearchParams.set("task_id", data.task_id);
                  router.push(`${pathname}?${newSearchParams.toString()}`);
                }

                if (data.planned_job_id) {
                  setPlannedTaskId(data.planned_job_id);
                  connectToSession(data.planned_job_id);
                }

                if (data.message) {
                  setLogInfo(data.message);
                }

                if (data.end === true) {
                  setShowManifestEditor(false);
                  
                  if (data.task_id) {
                    setIsCompleted(true);
                    setTimeout(() => {
                      if (data.iframe) {
                        setCurrentIframeUrl(data.iframe);
                      }
                      const newSearchParams = new URLSearchParams(searchParams);
                      newSearchParams.set("task_id", data.task_id);
                      router.push(`${pathname}?${newSearchParams.toString()}`);
                      setIsVisible(true);
                      setIsGeneratingCode(false);
                      setIsInitiating(false);
                      fetchData();
                    }, 3000);
                  } else {
                    showAlert(data.message || data.error || "Unable to start task", "error");
                    setIsGeneratingCode(false);
                    setIsInitiating(false);
                  }
                  abortController.abort();
                }
              } catch (error) {
                abortController.abort();
                setShowManifestEditor(false);
                showAlert("Error processing response", "error");
                setIsGeneratingCode(false);
                setIsInitiating(false);
              }
            },
            onerror: (error) => {
              showAlert("Error in code generation: " + error, "error");
              if (plannedTaskId) {
                disconnectFromSession(plannedTaskId);
              }
              abortController.abort();
              setShowManifestEditor(false);
              setIsGeneratingCode(false);
              setIsInitiating(false);
              hasGeneratedCode.current = false;
              return null;
            },
            onclose: () => {
              if (plannedTaskId) {
                disconnectFromSession(plannedTaskId);
              }
              if (!isCompleted) {
                setShowManifestEditor(false);
              }
              setIsGeneratingCode(false);
              setController(null);
              setIsInitiating(false);

              if (!isCompleted) {
                hasGeneratedCode.current = false;
              }

              fetchData();
            }
          }
        );
      }
    } catch (error) {
      if (plannedTaskId) {
        disconnectFromSession(plannedTaskId);
      }
      abortController.abort();
      setShowManifestEditor(false);
      showAlert("Failed to start code generation", "error");
      setIsGeneratingCode(false);
      setIsInitiating(false);
      hasGeneratedCode.current = false;
    }
  };

  const confirmClose = () => {
    if (controller) controller.abort();
    setIsGeneratingCode(false);
    setIsInitiating(false);
    hasGeneratedCode.current = false;
    setIsVisible(false);
    setCurrentIframeUrl(null);
    setPlannedTaskId(null);
  };

  const fetchData = async () => {
    setLoading(true);
    setError(null);
    try {
      const data = await fetchSystemContextWithContainers(projectId);

      let filteredContainers = data?.data?.containers?.filter(
        item =>
          item.properties?.ContainerType?.toLowerCase() === "internal" ||
          item.properties?.ContainerType?.toLowerCase() === "external" ||
          item.properties?.ContainerType === undefined
      );
      let filteredData = { ...data, data: { ...data.data, containers: filteredContainers } };
      sessionStorage.setItem(
        "project name",
        data?.data?.systemContext?.properties?.Title
      );

      const reconfig = await getReconfigNodeStatus(projectId);
      let showBanner = false;

      if (reconfig) {
        const hasReconfigNeeded =
        reconfig.Container?.some(item => item.reconfig_needed === true);
        const Count =
        (reconfig.Container?.filter(item => item.reconfig_needed === true)?.length || 0);

        setReconfigCount(Count);
        showBanner = hasReconfigNeeded;
      }
      setShowBaner(showBanner);

      setContainerList(filteredData);
      setContainerData(filteredData?.data?.containers);
    } catch (error) {
      setError(error);
    } finally {
      setLoading(false);
    }
  };

  // Function to fetch all branches for a specific container
  const fetchAllBranchesForContainer = async (containerId, onComplete = null) => {
    const containerKey = `container_${containerId}`;
    
    try {
      setContainerBranches(prev => ({
        ...prev,
        [containerKey]: {
          ...prev[containerKey],
          isFetching: true,
          error: false
        }
      }));

      let allBranches = [];
      let currentPage = 1;
      let totalPages = 1;
      const perPage = 100;

      do {
        const response = await listAllBranches(projectId, containerId, currentPage, perPage);
        
        if (response?.detail === "404: Repository not found") {
          setContainerBranches(prev => ({
            ...prev,
            [containerKey]: {
              branches: [],
              allBranchesFetched: true,
              isFetching: false,
              error: false,
              pagination: { currentPage: 1, totalPages: 1, perPage, totalCount: 0 }
            }
          }));
          return [];
        }
        
        if (response.branches && response.branches.length > 0) {
          allBranches = [...allBranches, ...response.branches];
        }
        
        totalPages = response.pagination?.total_pages || 1;
        currentPage++;
      } while (currentPage <= totalPages);

      setContainerBranches(prev => ({
        ...prev,
        [containerKey]: {
          branches: allBranches,
          allBranchesFetched: true,
          isFetching: onComplete ? true : false,
          error: false,
          pagination: {
            currentPage: 1,
            totalPages: Math.ceil(allBranches.length / 30),
            perPage: 30,
            totalCount: allBranches.length
          }
        }
      }));

      if (onComplete) {
        await onComplete();
        setContainerBranches(prev => ({
          ...prev,
          [containerKey]: {
            ...prev[containerKey],
            isFetching: false
          }
        }));
      }

      return allBranches;
    } catch (error) {
      console.error("Error fetching all branches:", error);
      setContainerBranches(prev => ({
        ...prev,
        [containerKey]: {
          branches: [],
          allBranchesFetched: true,
          isFetching: false,
          error: false,
          pagination: { currentPage: 1, totalPages: 1, perPage: 30, totalCount: 0 }
        }
      }));
      return [];
    }
  };

  const getPaginatedBranches = (containerId, page = 1, pageSize = 30) => {
    const containerKey = `container_${containerId}`;
    const containerData = containerBranches[containerKey];
    
    if (!containerData || !containerData.branches) {
      return {
        branches: [],
        pagination: { currentPage: 1, totalPages: 1, perPage: pageSize, totalCount: 0 }
      };
    }

    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const paginatedBranches = containerData.branches.slice(startIndex, endIndex);
    
    return {
      branches: paginatedBranches,
      pagination: {
        currentPage: page,
        totalPages: Math.ceil(containerData.branches.length / pageSize),
        perPage: pageSize,
        totalCount: containerData.branches.length
      }
    };
  };

  const fetchRepositoryDetails = async (containerId, actionType = 'view') => {
    const loadingKey = `${containerId}-${actionType}`;
    
    try {
      setLoadingStates(prev => ({ ...prev, [loadingKey]: true }));
      
      setRepositoryState({
        state: 'loading',
        data: null
      });

      const [repoResponse, allBranches] = await Promise.all([
        getRepository(projectId, containerId),
        fetchAllBranchesForContainer(containerId)
      ]);

      if (repoResponse.repository) {
        setRepositoryState({
          state: 'success',
          data: repoResponse.repository
        });
        setIsRepoConfigured(true);
        
        if (actionType === 'config') {
          onConfigureContainer(containerList.data.containers.find(c => c.id === containerId));
        } else if (actionType === 'view') {
          setShowRepoDetails(true);
        }
      } else {
        setRepositoryState({
          state: 'error',
          data: null
        });
        setIsRepoConfigured(false);
        
        if (actionType === 'config') {
          onConfigureContainer(containerList.data.containers.find(c => c.id === containerId));
        } else if (actionType === 'view') {
          setShowRepoDetails(true);
        }
      }
    } catch (err) {
      setRepositoryState({
        state: 'error',
        data: null
      });
      setIsRepoConfigured(false);
      console.error("Error fetching repository details:", err);
      
      if (actionType === 'config') {
        onConfigureContainer(containerList.data.containers.find(c => c.id === containerId));
      } else if (actionType === 'view') {
        setShowRepoDetails(true);
      }
    } finally {
      setLoadingStates(prev => ({ ...prev, [loadingKey]: false }));
    }
  };

  const handlePropertyUpdate = async (key, value) => {
    try {
      if (!selectedContainerId) {
        console.warn('handlePropertyUpdate called with empty selectedContainerId', { key, value });
        return;
      }

      const response = await updateNodeByPriority(selectedContainerId, key, value);

      if (response === "success") {
        setSelectedContainer(prev => ({
          ...prev,
          properties: {
            ...prev.properties,
            [key]: value
          }
        }));

        setContainerList(prevList => {
          const updatedContainers = prevList.data.containers.map(container => {
            if (container.id === selectedContainerId) {
              return {
                ...container,
                properties: {
                  ...container.properties,
                  [key]: value
                }
              };
            }
            return container;
          });

          return {
            ...prevList,
            data: {
              ...prevList.data,
              containers: updatedContainers
            }
          };
        });

      } else {
        throw new Error('Update failed');
      }
    } catch (error) {
      console.error('Error in handlePropertyUpdate:', error, { selectedContainerId, key, value });
      showAlert("Failed to update content", "error");
    }
  };

  const handleRowClick = (containerId) => {
    router.push(buildProjectUrl(projectId, `architecture/container/${containerId}`));
  };

  const handleRepoChangeInModal = (updatedRepo) => {
    setRepositoryState({
      state: 'success',
      data: updatedRepo
    });
    
    setSelectedContainer(prev => ({
      ...prev,
      repositoryName: updatedRepo.repositoryName,
      repository_name: updatedRepo.repositoryName,
      project_name: updatedRepo.repositoryName,
      cloneUrlHttp: updatedRepo.cloneUrlHttp,
      http_url: updatedRepo.cloneUrlHttp,
      branch: updatedRepo.branch || prev.properties?.branch || null,
      properties: {
        ...prev.properties,
        repositoryName: updatedRepo.repositoryName,
        repository_name: updatedRepo.repositoryName,
        project_name: updatedRepo.repositoryName,
        cloneUrlHttp: updatedRepo.cloneUrlHttp,
        http_url: updatedRepo.cloneUrlHttp,
        branch: updatedRepo.branch || prev.properties?.branch || null
      }
    }));

    setContainerList(prevList => {
      const updatedContainers = prevList.data.containers.map(container => {
        if (container.id === selectedContainerId) {
          return {
            ...container,
            repositoryName: updatedRepo.repositoryName,
            repository_name: updatedRepo.repositoryName,
            project_name: updatedRepo.repositoryName,
            cloneUrlHttp: updatedRepo.cloneUrlHttp,
            http_url: updatedRepo.cloneUrlHttp,
            branch: updatedRepo.branch || container.properties?.branch || null,
            properties: {
              ...container.properties,
              repositoryName: updatedRepo.repositoryName,
              repository_name: updatedRepo.repositoryName,
              project_name: updatedRepo.repositoryName,
              cloneUrlHttp: updatedRepo.cloneUrlHttp,
              http_url: updatedRepo.cloneUrlHttp,
              branch: updatedRepo.branch || container.properties?.branch || null
            }
          };
        }
        return container;
      });

      return {
        ...prevList,
        data: {
          ...prevList.data,
          containers: updatedContainers
        }
      };
    });

    if (selectedContainerId && updatedRepo.branch) {
      fetchAllBranchesForContainer(selectedContainerId);
    }

    showAlert('Repository configured successfully', 'success');
  };

  const onConfigureContainer = (container) => {
    const updatedContainer = containerList.data.containers.find(c => c.id === container.id) || container;
    setSelectedContainer(updatedContainer);
    setSelectedContainerId(updatedContainer.id);

    if (updatedContainer.properties.platform) {
      const platformData = PLATFORMS.find(p => p.key === updatedContainer.properties.platform);
      setCurrentPlatform(platformData ? {
        key: platformData.key,
        label: platformData.label,
        icon: platformData.icon
      } : { key: "generic", label: "Generic", icon: <Generic /> });
    } else {
      setCurrentPlatform({ key: "generic", label: "Generic", icon: <Generic /> });
    }

    if (updatedContainer.properties.framework) {
      const frameworkData = frameworks.find(f => f.key === updatedContainer.properties.framework) ||
                          backendFrameworks.find(f => f.key === updatedContainer.properties.framework) ||
                          mobileFrameworks.find(f => f.key === updatedContainer.properties.framework) || 
                          databaseFrameworks.find(f => f.key === updatedContainer.properties.framework);
      if (frameworkData) {
        setCurrentFramework(frameworkData);
      } else {
        setCurrentFramework(frameworks[0]);
      }
    } else {
      setCurrentFramework(frameworks[0]);
    }

    setCodeGenSetupModal(true);
  };

  const handleUpdateConfiguration = () => {
    fetchData();
    setCodeGenSetupModal(false);
    showAlert('Configuration updated successfully', 'success');
  };

  const handleStartCodeGeneration = async () => {
    if (!selectedContainers.all_containers && selectedContainers.containers.length === 0) {
      showAlert('Please select at least one container', 'error');
      return;
    }

    // Open the ManifestEditor
    setShowManifestEditor(true);
  };

  const handleManifestSave = (response, formData) => {
    showAlert("Manifest saved successfully!", "success");
  };

  const handleManifestError = (error) => {
    showAlert(error?.message || "Failed to save manifest", "error");
  };

  const handlePlatformChange = (platformData) => {
    setCurrentPlatform(platformData);

    if (selectedContainerId) {
      handlePropertyUpdate("platform", platformData.key);
    }

    if (platformData.key === "mobile") {
      setCurrentFramework(mobileFrameworks[0]);
      if (selectedContainerId) {
        handlePropertyUpdate("framework", mobileFrameworks[0].key);
      }
    } else if (platformData.key === "web") {
      setCurrentFramework(frameworks[0]);
      if (selectedContainerId) {
        handlePropertyUpdate("framework", frameworks[0].key);
      }
    } else if (platformData.key === "backend") {
      setCurrentFramework(backendFrameworks[0]);
      if (selectedContainerId) {
        handlePropertyUpdate("framework", backendFrameworks[0].key);
      }
    } else if (platformData.key === "database") {  
      setCurrentFramework( databaseFrameworks[0]);
      if (selectedContainerId) {
        handlePropertyUpdate("framework", databaseFrameworks[0].key);
      }
    }
    
    
    else {
      setCurrentFramework(frameworks[0]);
      if (selectedContainerId) {
        handlePropertyUpdate("framework", frameworks[0].key);
      }
    }
  };

  const handleFrameworkChange = (newFramework) => {
    setCurrentFramework(newFramework);

    if (selectedContainerId) {
      handlePropertyUpdate("framework", newFramework.key);
    }
  };

  const handleBranchUpdate = async (newBranch) => {
    try {
      await handlePropertyUpdate("branch", newBranch);
    } catch (error) {
      console.error(error);
      showAlert('Failed to update branch', 'error');
    }
  };

  const BranchSelection = () => {
    if (!selectedContainerId) {
      console.warn('BranchSelection rendered without selectedContainerId');
    }

    const containerKey = `container_${selectedContainerId}`;
    const containerData = containerBranches[containerKey] || {
      branches: [],
      isFetching: false,
      error: false,
      pagination: { currentPage: 1, totalPages: 1, perPage: 30, totalCount: 0 }
    };

    const handleFetchBranches = async (page = 1) => {
      return getPaginatedBranches(selectedContainerId, page);
    };

    const handleForceRefreshBranches = async (newBranchName = null) => {
      if (selectedContainerId) {
        await fetchAllBranchesForContainer(selectedContainerId, async () => {
          await new Promise(resolve => setTimeout(resolve, 300));
          
          if (newBranchName) {
            await handlePropertyUpdate("branch", newBranchName);
          }
        });
      }
    };

    const { branches: paginatedBranches, pagination } = getPaginatedBranches(selectedContainerId, containerData.pagination?.currentPage || 1);

    const currentContainer = containerList?.data?.containers?.find(c => c.id === selectedContainerId);
    const currentBranch = currentContainer?.properties?.branch || selectedContainer?.properties?.branch || null;

    return (
      <BranchSelector
        key={`branch-selector-${selectedContainerId || 'default'}-${currentBranch || 'none'}`}
        projectId={projectId}
        containerId={selectedContainerId}
        currentBranch={currentBranch}
        onUpdate={handleBranchUpdate}
        className="w-full"
        branches={paginatedBranches}
        pagination={pagination}
        onFetchBranches={handleFetchBranches}
        isFetchingBranches={containerData.isFetching}
        repoError={containerData.error}
        onForceRefreshBranches={handleForceRefreshBranches}
      />
    );
  };

  const handleRepoDetailsOpen = (containerId) => {
    setSelectedContainer(containerList.data.containers.find(c => c.id === containerId));
    setSelectedContainerId(containerId);
    fetchRepositoryDetails(containerId, 'view');
  };

  const handleRepoDetailsClose = (success = false) => {
    setShowRepoDetails(false);
    if (success && selectedContainerId) {
      setIsRepoConfigured(true);
      showAlert('Repository configured successfully', 'success');
      fetchRepositoryDetails(selectedContainerId, 'refresh');
      // Refresh the container list to show updated repository URLs
      fetchData();
    }
  };

  const handleSetPastTasks = (tasks) => {
    setPastTasks([...tasks]);
  };

  const fetchPastTasks = async (currentSkip = 0, currentLimit = limit) => {
    if (!selectedContainerId) {
      console.warn('fetchPastTasks called without selectedContainerId');
      return;
    }

    setIsPastTaskLoading(true);
    try {
      const result = await getPastCodeGenerationTasks(
        projectId,
        selectedContainerId,
        currentLimit,
        currentSkip
      );
      handleSetPastTasks(result.tasks);
      setTotalCount(result.total_count);
      setSkip(currentSkip);
      setLimit(currentLimit);
    } catch (error) {
      console.error('Error fetching past tasks:', error, { selectedContainerId });
      showAlert("Failed to fetch past code generation tasks", "error");
    } finally {
      setIsPastTaskLoading(false);
    }
  };

  const handleViewPastCodeGeneration = async () => {
    await fetchPastTasks();
    setIsPastTasksCodeModalOpen(true);
  };

  const handlePageChange = async (newPage) => {
    const newSkip = (newPage - 1) * limit;
    await fetchPastTasks(newSkip, limit);
  };

  const handleLimitChange = async (newLimit) => {
    await fetchPastTasks(0, newLimit);
  };

  const fetchHistoryTasks = useCallback(async (page = 1, pageSize = 10, filters = {}, isRefresh = false) => {
    try {
      if (!isRefresh) {
        setIsHistoryLoading(true);
      }
      const skip = (page - 1) * pageSize;

      const queryParams = new URLSearchParams();
      if (filters.search) queryParams.append('search', filters.search);
      if (filters.type) queryParams.append('type', filters.type);
      if (filters.status) queryParams.append('status', filters.status);
      if (filters.created_at) {
        const formattedDate = dayjs(filters.created_at).format('YYYY-MM-DD');
        queryParams.append('created_at', formattedDate);
      }

      const tasks = await getPastCodeTasks(
        projectId,
        pageSize,
        skip,
        queryParams.toString()
      );

      const transformedData = transformSessionsResponse(tasks);
      setSessions(transformedData.sessions);
      setHistoryPagination(prev => ({
        ...prev,
        totalItems: transformedData.pagination.total_count
      }));
    } catch (error) {
      console.error('Error fetching history tasks:', error);
      throw error;
    } finally {
      if (!isRefresh) {
        setIsHistoryLoading(false);
      }
    }
  }, [projectId]);

  const handleDirectRefresh = useCallback(async () => {
    try {
      await fetchHistoryTasks(
        historyPagination.currentPage,
        historyPagination.pageSize,
        historyFilters
      );
    } catch (error) {
      throw error;
    }
  }, [fetchHistoryTasks, historyPagination.currentPage, historyPagination.pageSize, historyFilters]);

  const handleHistoryPageChange = (newPage) => {
    setHistoryPagination(prev => ({ ...prev, currentPage: newPage }));
  };

  const handleHistoryPageSizeChange = (newSize) => {
    setHistoryPagination(prev => ({
      ...prev,
      pageSize: newSize,
      currentPage: 1
    }));
  };

  const handleHistoryFilterChange = useCallback((newFilters) => {
    setHistoryFilters(newFilters);
    setHistoryPagination(prev => ({ ...prev, currentPage: 1 }));
  }, []);

  const handleHistoryOpen = () => {
    fetchHistoryTasks(historyPagination.currentPage, historyPagination.pageSize, historyFilters);
    setIsHistoryOpen(true);
  };

  const handleUpdateContainerList = () => {
    const newSearchParams = new URLSearchParams(searchParams);
    newSearchParams.set("discussion", "new");
    newSearchParams.set("node_id", projectId);
    newSearchParams.set("node_type", "Architecture");
    router.push(`${pathname}?${newSearchParams.toString()}`);
  };

  const updateProps = {
    onUpdateClick: handleUpdateContainerList,
    buttonText: "Update Container List",
  };

  const handleBack = () => {
    router.back();
  };

  const HeaderSection = () => (
    <div className="flex flex-col space-y-4 top-1" style={{ zIndex: 5 }}>
      <div className="flex flex-col border border-gray-200">
        <div className="relative px-2.5 py-1 space-y-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <IconButton
                icon={<ArrowLeft className="w-5 h-5 text-gray-600" />}
                tooltip="Go back"
                onClick={handleBack}
                className="hover:bg-gray-100"
              />
              <div className="flex items-center gap-2">
                <h2 className="typography-body-lg font-weight-semibold text-gray-800">
                  {containerList?.data?.systemContext?.properties?.Title || "List of all containers"}
                </h2>
                {containerList?.data?.systemContext?.properties?.Type && (
                  <div className="flex items-center gap-1">
                    <Badge type={"Containers List"} />
                  </div>
                )}
              </div>
            </div>
          </div>
          <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-primary-100/50 via-primary-300/20 to-transparent"></div>
        </div>
      </div>
    </div>
  );

  useEffect(() => {
    fetchData();
  }, [projectId]);

  useEffect(() => {
    if (searchParams.get("task_id")) {
    setCodeGenSetupModal(false);
    setIsGeneratingCode(false);
    }
  },[searchParams])

  const handleSelectAll = () => {
    setSelectedContainers(prev => {
      const newAllContainers = !prev.all_containers;
      setIsAllSelected(newAllContainers);
      return {
        all_containers: newAllContainers,
        containers: []
      };
    });
  };

  const handleContainerSelect = (container) => {
    setSelectedContainers(prev => {
      const containerId = String(container.id);
      const currentContainers = prev.containers.map(id => String(id));
      
      if (prev.all_containers) {
        const allContainerIds = containerList.data.containers.map(c => String(c.id));
        return {
          all_containers: false,
          containers: allContainerIds.filter(id => id !== containerId)
        };
      } else {
        const newContainers = currentContainers.includes(containerId)
          ? currentContainers.filter(id => id !== containerId)
          : [...currentContainers, containerId];

        const allSelected = newContainers.length === containerList.data.containers.length;
        return {
          all_containers: allSelected,
          containers: allSelected ? [] : newContainers
        };
      }
    });
  };

  useEffect(() => {
    if (!selectedContainers.all_containers) {
      setIsAllSelected(
        selectedContainers.containers &&
        containerList?.data?.containers &&
        selectedContainers.containers.length === containerList.data.containers.length &&
        containerList.data.containers.length > 0
      );
    }
  }, [selectedContainers.containers, selectedContainers.all_containers, containerList?.data?.containers]);

  useEffect(() => {
    setSelectedContainers({
      all_containers: false,
      containers: []
    });
    setIsAllSelected(false);
  }, [projectId]);

  useEffect(() => {
    if (!isModal) return;

    const selectAllBtn = document.getElementById('generation-select-all-btn');
    const historyBtn = document.getElementById('generation-history-btn');
    const startSessionBtn = document.getElementById('generation-start-session-btn');

    if (selectAllBtn) {
      const checkbox = selectAllBtn.querySelector('div > div');
      const span = selectAllBtn.querySelector('span');

      if (selectedContainers.all_containers) {
        checkbox.classList.add('bg-primary', 'border-primary');
        checkbox.classList.remove('border-gray-400');
        checkbox.innerHTML = `
          <svg width="8" height="8" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M8.5 3L4 7.5L1.5 5" stroke="white" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
          </svg>
        `;
        span.textContent = 'Deselect All';
      } else {
        checkbox.classList.remove('bg-primary', 'border-primary');
        checkbox.classList.add('border-gray-400');
        checkbox.innerHTML = '';
        span.textContent = 'Select All';
      }

      selectAllBtn.onclick = handleSelectAll;
    }

    if (startSessionBtn) {
      const isDisabled = selectedContainers.all_containers
        ? false
        : selectedContainers.containers.length === 0;

      startSessionBtn.disabled = isDisabled || isInitiating;

      if (isInitiating) {
        startSessionBtn.innerHTML = `
          <svg class="animate-spin -ml-1 mr-2 h-3.5 w-3.5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          <span>Starting...</span>
        `;
        startSessionBtn.classList.add('opacity-75', 'cursor-not-allowed');
        startSessionBtn.classList.remove('hover:bg-primary-600');
      } else {
        startSessionBtn.innerHTML = '<span>Start Session</span>';
        
        if (isDisabled) {
          startSessionBtn.classList.add('opacity-50', 'cursor-not-allowed');
          startSessionBtn.classList.remove('hover:bg-primary-600');
        } else {
          startSessionBtn.classList.remove('opacity-50', 'cursor-not-allowed', 'opacity-75');
          startSessionBtn.classList.add('hover:bg-primary-600');
        }
      }

      if (!isInitiating) {
        startSessionBtn.onclick = handleStartCodeGeneration;
      } else {
        startSessionBtn.onclick = null;
      }
    }
  }, [selectedContainers, isInitiating, isModal]);

  useEffect(() => {
    fetchHistoryTasks(historyPagination.currentPage, historyPagination.pageSize, historyFilters);
  }, [projectId, historyPagination.currentPage, historyPagination.pageSize, historyFilters]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (controller) {
        controller.abort();
      }
      if (plannedTaskId) {
        disconnectFromSession(plannedTaskId);
      }
    };
  }, []);

  if (loading) {
    return <CardGroupSkeletonLoder />;
  }

  if (error) {
    return (
      <ErrorView
        title="Unable to Load Container List"
        message={en.UnableToLoadContainer}
        onRetry={() => fetchData()}
        panelType='main'
      />
    );
  }

  if (!containerList?.data?.containers?.length || (isModal && !(containerList?.data?.containers?.some(
  item => item.properties?.ContainerType?.toLowerCase() == "internal" || item.properties?.ContainerType === undefined
))
)) {
    return <EmptyStateView type="containers" />;
  }

  return (
    <div className="h-full">
      {!isModal && <HeaderSection />}
      
      {showBaner && (
        <RequirementsBanner value={`${reconfigCount} container${reconfigCount > 1 ? 's' : ''} `} />
      )}

      {isModal && <CodeGenerationInfo />}

      <GenericCardGrid
        isModal={isModal}
        data={containerList.data.containers.map(container => ({
          container_name: container.properties.Title,
          components: [{
            container: {
              title: container.properties.Title,
              id: container.id,
            },
            ...container,
            ...container.properties
          }]
        }))}
        onCardClick={(container) => handleRowClick(container.id)}
        actionButtons={[
          {
            icon: <Eye className="h-4 w-4" />,
            label: "View",
            onClick: (comp) => handleRowClick(comp.id),
            className: "bg-gray-50 text-gray-600 hover:bg-gray-100"
          },
          {
            icon: <FolderGit2 className="h-4 w-4" />,
            label: "Repository",
            onClick: (comp) => handleRepoDetailsOpen(comp.id),
            className: "bg-primary-50 text-primary hover:bg-primary-100"
          },
          {
            icon: <CodeXml className="h-4 w-4" />,
            label: "Config",
            onClick: (comp) => {
              setSelectedContainer(comp);
              setSelectedContainerId(comp.id);
              fetchRepositoryDetails(comp.id, 'config');
            },
            className: "bg-primary-50 text-primary-600 hover:bg-primary-100"
          }
        ]}
        uniquePageIdentifier={`arch-container-list-${projectId}`}
        loadingStates={loadingStates}
        selectedContainers={selectedContainers}
        onContainerSelect={handleContainerSelect}
      />

      {/* Repository Modal */}
      {showRepoDetails && selectedContainerId && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-4 rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <RepositoryDetailsModal
              open={true}
              projectId={projectId}
              containerId={selectedContainerId}
              onClose={handleRepoDetailsClose}
              onSuccess={() => handleRepoDetailsClose(true)}
              branches={containerBranches[`container_${selectedContainerId}`]?.branches || []}
              selectedBranch={selectedContainer?.properties?.branch}
              onBranchSelect={handleBranchUpdate}
            />
          </div>
        </div>
      )}

      {/* Code Generation Setup Modal */}
      {codeGenSetupModal && selectedContainerId && (
        <CodeGenerationSetupModal
          onClose={() => setCodeGenSetupModal(false)}
          onConfigureRepo={() => handleRepoDetailsOpen(selectedContainerId)}
          BranchSelection={BranchSelection}
          currentPlatform={currentPlatform}
          onPlatformChange={handlePlatformChange}
          onConfirm={handleUpdateConfiguration}
          repository={repositoryState.data}
          currentBranch={selectedContainer?.properties?.branch}
          currentFramework={currentFramework}
          onFrameworkChange={handleFrameworkChange}
          isGeneratingCode={false}
          projectId={projectId}
          containerId={parseInt(selectedContainerId)}
          handleRepoChange={handleRepoChangeInModal}
          isModal={isModal}
          isConfigMode={true}
          hideActionButton={true}
          selectedContainers={selectedContainers.all_containers 
            ? containerList.data.containers.map(c => ({
                id: c.id,
                title: c.properties.Title,
                properties: c.properties
              }))
            : selectedContainers.containers.map(containerId => {
                const container = containerList.data.containers.find(c => String(c.id) === String(containerId));
                return container ? {
                  id: container.id,
                  title: container.properties.Title,
                  properties: container.properties
                } : null;
              }).filter(Boolean)
          }
          isMultiContainer={selectedContainers.all_containers || selectedContainers.containers.length > 1}
        />
      )}

      {/* New ManifestEditor Component */}
      {showManifestEditor && (
        <ManifestEditor
          projectId={projectId}
          onSave={handleManifestSave}
          onError={handleManifestError}
          onClose={() => setShowManifestEditor(false)}
          isOpen={showManifestEditor}
          selectedContainerIds={
            selectedContainers.all_containers 
              ? containerList.data.containers.filter((container)=>{
                // Filer only internal containers
                if (container.properties?.ContainerType?.toLowerCase() === "internal" || container.properties?.ContainerType === undefined) {
                  return true;
                }
                return false;
              }).map(c =>c.id)
              : selectedContainers.containers.map(id => parseInt(id))
          }
          customButtonLabel="Start Code Generation"
          customButtonAction={() => {
            setShowManifestEditor(false);
            proceedWithCodeGeneration();
          }}
        />
      )}

      {/* Code Generation Loading Modal */}
      {isInitiating && (
        <div className="fixed inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-center justify-center">
          <div className="bg-white rounded-lg shadow-xl p-8 max-w-md w-full mx-4">
            <div className="text-center">
              <div className="w-16 h-16 border-4 border-gray-200 border-t-primary-600 rounded-full animate-spin mx-auto mb-4"></div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Starting Code Generation</h3>
              <p className="text-gray-600 mb-4">
                {logInfo || "Initializing code generation session..."}
              </p>
              <div className="text-sm text-gray-500">
                This may take a few moments to set up your environment.
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Code Generation Modal */}
      {isVisible && <CodeGenerationModal />}

      {/* Past Tasks Modal */}
      {isPastTasksCodeModalOpen && (
        <PastTasksModal
          isOpen={isPastTasksCodeModalOpen}
          onClose={() => setIsPastTasksCodeModalOpen(false)}
          tasks={pastTasks}
          totalCount={totalCount}
          limit={limit}
          skip={skip}
          onPageChange={handlePageChange}
          onLimitChange={handleLimitChange}
          isLoading={isPastTaskLoading}
        />
      )}

      {/* History Modal */}
      {isHistoryOpen && (
        <div className="fixed inset-0 z-50 bg-black bg-opacity-50 flex items-center justify-center backdrop-blur-sm">
          <div className="bg-white rounded-lg w-[94vw] h-[94vh] flex flex-col shadow-xl">
            <div className="flex-1 overflow-hidden flex flex-col">
              <div className="flex-1 overflow-y-auto custom-scrollbar p-2">
                <Sessions
                  initialSessions={sessions}
                  isLoading={isHistoryLoading}
                  onFilterChange={handleHistoryFilterChange}
                  onRefresh={handleDirectRefresh}
                  onCloseModal={() => setIsHistoryOpen(false)}
                  compactMode={true}
                />
              </div>
              <div className="sticky bottom-0 bg-white border-t border-gray-200 py-3 px-4">
                <div className="flex items-center justify-between">
                  <div className="typography-caption text-gray-500">
                    Showing {sessions.length} of {historyPagination.totalItems} sessions
                  </div>
                  <Pagination
                    currentPage={historyPagination.currentPage}
                    pageCount={Math.max(1, Math.ceil(historyPagination.totalItems / historyPagination.pageSize))}
                    pageSize={historyPagination.pageSize}
                    totalItems={historyPagination.totalItems}
                    onPageChange={handleHistoryPageChange}
                    onPageSizeChange={handleHistoryPageSizeChange}
                    pageSizeOptions={[5, 10, 20, 50]}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ContainerList;