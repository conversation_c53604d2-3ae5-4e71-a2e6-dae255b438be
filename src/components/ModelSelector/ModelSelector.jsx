import React, { useState, useEffect, useRef } from 'react';
import { ChevronDown, Check, Loader2, <PERSON>, <PERSON><PERSON>, Gauge, Rocket } from 'lucide-react';
import { BootstrapTooltip } from "@/components/UIComponents/ToolTip/Tooltip-material-ui";

const ModelSelector = ({ 
  selectedModel, 
  onSelectModel, 
  availableModels = [], 
  isDisabled = false, 
  isLoading = false, 
  reasoningOptions = [], 
  reasoningEffort = null,
  onReasoningEffortChange = null 
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [showReasoningDropdown, setShowReasoningDropdown] = useState(false);
  const dropdownRef = useRef(null);
  const reasoningDropdownRef = useRef(null);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
      if (reasoningDropdownRef.current && !reasoningDropdownRef.current.contains(event.target)) {
        setShowReasoningDropdown(false);
      }
    };

    if (isOpen || showReasoningDropdown) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen, showReasoningDropdown]);

  const modelNameMap = {
    gpt_4o: "GPT-4o",
    gpt_4_1: "GPT-4.1",
    gpt_4_1_mini: "GPT-4.1 Mini",
    claude_3_5_sonnet: "Claude 3.5 Sonnet",
    claude_3_7_sonnet: "Claude 3.7 Sonnet",
    claude_3_5_haiku: "Claude 3.5 Haiku",
    "Claude 4.0": "Claude 4.0",
    "GPT-4.1": "GPT-4.1",
    "Azure GPT-4.1": "Azure GPT-4.1",
    "O3": "O3",
    "gpt-5": "GPT-5",
    "Gemini-2.5-pro": "Gemini-2.5-pro",
    "Claude.4..0": "Claude 4.0",
    "GPT-.4..1": "GPT-4.1",
    "Azure GPT-.4..1": "Azure GPT-4.1",
    "Gemini-.2..5-Pro": "Gemini-2.5-pro"
  };

  const reasoningEffortMap = {
    minimal: { label: "Minimal", icon: Zap, color: "text-green-600", description: "Fastest, basic reasoning" },
    low: { label: "Low", icon: Gauge, color: "text-blue-600", description: "Quick reasoning" },
    medium: { label: "Medium", icon: Brain, color: "text-orange-600", description: "Balanced reasoning" },
    high: { label: "High", icon: Rocket, color: "text-red-600", description: "Deep reasoning" }
  };

  function getDisplayModelName(model) {
    return modelNameMap[model] || model
      .replace(/_/g, " ")
      .replace(/\b\w/g, (c) => c.toUpperCase());
  }

  const currentModel = selectedModel || (availableModels.length > 0 ? availableModels[0] : "GPT-4.1");
  const hasReasoningOptions = reasoningOptions && reasoningOptions.length > 0;
  const currentReasoningConfig = reasoningEffort && reasoningEffortMap[reasoningEffort];

  const handleReasoningEffortSelect = (effort) => {
    if (onReasoningEffortChange) {
      onReasoningEffortChange(effort);
    }
    setShowReasoningDropdown(false);
  };

  return (
    <div className="flex items-center gap-2">
      {/* Model Selector */}
      <div className="relative inline-block" ref={dropdownRef}>
        <BootstrapTooltip 
          title={isDisabled ? "Waiting for available models..." : "Select model for code generation"} 
          placement="bottom"
        >
          <button
            className={`flex items-center justify-between gap-1.5 px-3 py-1 typography-caption font-weight-medium bg-white border border-gray-200 rounded-md shadow-sm transition-colors min-w-[140px] h-[32px] ${
              isDisabled ? 'opacity-60 cursor-not-allowed' : 'hover:bg-gray-50 cursor-pointer'
            }`}
            onClick={() => !isDisabled && setIsOpen(!isOpen)}
            disabled={isDisabled}
          >
            {isLoading ? (
              <>
                <Loader2 size={14} className="animate-spin mr-1" />
                <span>Loading models...</span>
              </>
            ) : (
              <>
                <span className="truncate">
                  {getDisplayModelName(currentModel) || 'Select Model'}
                </span>
                <ChevronDown size={14} className={`transition-transform ${isOpen ? 'rotate-180' : ''}`} />
              </>
            )}
          </button>
        </BootstrapTooltip>

        {isOpen && !isDisabled && (
          <div className="absolute mt-1 z-50 min-w-full bg-white rounded-md shadow-md border border-gray-200 py-1">
            <div className="px-3 py-2 border-b border-gray-200">
              <h3 className="typography-caption font-weight-semibold text-gray-600 uppercase">Model Selection</h3>
            </div>
            {availableModels.map((model) => (
              <button
                key={model}
                className="flex items-center justify-between w-full px-3 py-2 typography-caption text-left hover:bg-gray-50 transition-colors"
                onClick={() => {
                  onSelectModel(model);
                  setIsOpen(false);
                }}
              >
                <div className="flex flex-col">
                  <span className="font-weight-medium text-gray-700">{getDisplayModelName(model)}</span>
                </div>
                {currentModel === model && (
                  <Check size={14} className="text-primary" />
                )}
              </button>
            ))}
          </div>
        )}
      </div>

      {/* Reasoning Effort Selector */}
      {hasReasoningOptions && (
        <div className="relative inline-block" ref={reasoningDropdownRef}>
          <BootstrapTooltip 
            title={currentReasoningConfig ? currentReasoningConfig.description : "Select reasoning effort"} 
            placement="bottom"
          >
            <button
              className={`flex items-center justify-between gap-1.5 px-3 py-1 typography-caption font-weight-medium bg-white border border-gray-200 rounded-md shadow-sm transition-colors min-w-[100px] h-[32px] ${
                isDisabled ? 'opacity-60 cursor-not-allowed' : 'hover:bg-gray-50 cursor-pointer'
              }`}
              onClick={() => !isDisabled && setShowReasoningDropdown(!showReasoningDropdown)}
              disabled={isDisabled}
            >
              {currentReasoningConfig ? (
                <>
                  <div className="flex items-center gap-1.5">
                    <currentReasoningConfig.icon size={12} className={currentReasoningConfig.color} />
                    <span className="truncate">{currentReasoningConfig.label}</span>
                  </div>
                  <ChevronDown size={14} className={`transition-transform ${showReasoningDropdown ? 'rotate-180' : ''}`} />
                </>
              ) : (
                <>
                  <div className="flex items-center gap-1.5">
                    <Brain size={12} className="text-gray-500" />
                    <span className="truncate">Reasoning</span>
                  </div>
                  <ChevronDown size={14} className={`transition-transform ${showReasoningDropdown ? 'rotate-180' : ''}`} />
                </>
              )}
            </button>
          </BootstrapTooltip>

          {showReasoningDropdown && !isDisabled && (
            <div className="absolute mt-1 z-50 min-w-full bg-white rounded-md shadow-md border border-gray-200 py-1">
              <div className="px-3 py-2 border-b border-gray-200">
                <h3 className="typography-caption font-weight-semibold text-gray-600">Reasoning effort</h3>
              </div>
              {reasoningOptions.map((effort) => {
                const config = reasoningEffortMap[effort];
                if (!config) return null;
                
                return (
                  <button
                    key={effort}
                    className="flex items-center justify-between w-full px-3 py-2 typography-caption text-left hover:bg-gray-50 transition-colors"
                    onClick={() => handleReasoningEffortSelect(effort)}
                  >
                    <div className="flex items-center gap-2">
                      <config.icon size={14} className={config.color} />
                      <div className="flex flex-col">
                        <span className="font-weight-medium text-gray-700">{config.label}</span>
                      </div>
                    </div>
                    {reasoningEffort === effort && (
                      <Check size={18} className="text-primary" />
                    )}
                  </button>
                );
              })}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default ModelSelector;