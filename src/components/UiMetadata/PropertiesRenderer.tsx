import React, { useState } from "react";
import dynamic from "next/dynamic";
import { formatUTCToLocal } from "@/utils/datetime";
import { Accordion } from "@/components/UIComponents/Accordions/Accordion";
import { renderHTML } from "@/utils/helpers";
import { Edit2 } from "lucide-react";
import APIDocumentation from "../API/API";
import { BootstrapTooltip } from "../UIComponents/ToolTip/Tooltip-material-ui";
const D2Chart = dynamic(() => import("../Chart/D2Chart"), { ssr: false });

const MarkdownEditor = dynamic(() => import("../Editor/MarkdownEditor"), {ssr: false});
const NoSSR = dynamic(() => import("../Chart/MermaidChart"), { ssr: false });

interface Metadata {
  [key: string]: {
    display_type?: string;
    hidden?: boolean;
    related_field?: string;
  };
}

interface Properties {
  [key: string]: any;
  IsArchitecturalLeaf?: string;
}

interface EditField {
  key: string;
  value: string;
  label: string;
}

interface PropertiesRendererProps {
  properties: Properties;
  metadata: Metadata;
  to_skip?: string[];
  to_show?: string[];
  projectId?: string;
  architectureId?: string;
  onUpdate?: (key: string, value: string) => Promise<void>;
}

const PropertiesRenderer: React.FC<PropertiesRendererProps> = ({
  properties,
  metadata,
  to_skip = [],
  to_show = [],
  projectId = "",
  architectureId = "",
  onUpdate,
}) => {
  const [isEditorOpen, setIsEditorOpen] = useState<boolean>(false);
  const [currentEditField, setCurrentEditField] = useState<EditField>({
    key: "",
    value: "",
    label: "",
  });
  const [activeToggle, setActiveToggle] = useState<{[key: string]: string}>({});

  const handleEdit = (key: string, value: string, label: string): void => {
    setCurrentEditField({ key, value, label });
    setIsEditorOpen(true);
  };

  const handleEditorClose = (): void => {
    setIsEditorOpen(false);
    setCurrentEditField({ key: "", value: "", label: "" });
  };

  const transformKey = (key: string): string => {
    const transformed = key
      .replace(/_/g, " ")
      .replace(/([a-z])([A-Z])/g, "$1 $2")
      .replace(/\b\w/g, (char) => char.toUpperCase());
    return transformed;
  };

  const shouldUseAccordion = (content: string): boolean => {
    if (!content || typeof content !== "string") return false;
    const lineCount = content?.split("\n").length;
    const wordCount = content?.split(/\s+/).length;
    return lineCount > 10 || wordCount > 200;
  };

  // Removed formatTechStack - let renderHTML handle all markdown formatting consistently

  const renderMarkdownContent = (content: string) => {
    if (typeof content !== "string") return content;

    let processedContent = content.replace(/\*\*(.*?)\*\*/g, "<strong>$1</strong>");
    processedContent = processedContent.replace(/<p>/g, "")
    processedContent = processedContent.replace(/<\/p>/g, "\n")
    processedContent = processedContent.replace(/<\/?ul>/g, "")
    processedContent = processedContent.replace(/<li>/g, "- ")
    processedContent = processedContent.replace(/<\/li>/g, "\n")
    processedContent = processedContent.replace(/ - /g, " \n- ")
    processedContent = processedContent.replace(/(?<!\n)(\b\d+\. )/g, "\n$1")
    processedContent = processedContent.replace(/(#+)\s*([^\n]+)\n/g, (match, hashes, text) => {
      const headingLevel = hashes.length;
      return `<h${headingLevel}>${text.trim()}</h${headingLevel}>\n`;
    });


    // Removed custom list processing - let renderHTML handle markdown formatting
    return processedContent;
  };


  const renderContent = (
    content: string,
    isTechStack: boolean,
    renderToHtml: boolean
  ): JSX.Element => {
    if (!content) return <></>;
    // Removed isTechStack special handling - let renderHTML handle all markdown formatting
    if (renderToHtml) {
      return <div dangerouslySetInnerHTML={{ __html: renderHTML(content) }} />;
    }
    return <>{content}</>;
  };

  const renderField = (key: string, value: any): JSX.Element | null => {
    if (!key || !metadata[key]) return null;

    const label = transformKey(key);

    if (metadata[key]?.hidden === true || to_skip.includes(key)) {
      return null;
    }

    const displayType = metadata[key]?.display_type || "text";
    const isTechStack =
      label === "Tech Stack Choices" || label === "Recommended Tech Stack";
    
    // Skip related fields that will be rendered with their primary field
    if (displayType === "type_toggle" && metadata[key]?.related_field) {
      const relatedField = metadata[key]?.related_field;
      // If this is the secondary field of a type_toggle pair, skip it
      const isSecondaryField = Object.keys(metadata).some(
        k => metadata[k]?.display_type === "type_toggle" && metadata[k]?.related_field === key
      );
      if (isSecondaryField) {
        return null;
      }
    }

    // Handle array values (like AcceptanceCriteria)
    let displayValue = value;
    if (Array.isArray(value)) {
      if (label === "Acceptance Criteria") {
        // Format AcceptanceCriteria as numbered list
        displayValue = value.map((item, index) => `${index + 1}. ${item}`).join('\n');
      } else {
        // For other arrays, join with newlines
        displayValue = value.join('\n');
      }
    }

    switch (label) {
      case "Title":
        return (
          <div key={key} className="bg-white rounded-lg p-4 mb-4">
            <span className="typography-body-lg font-weight-semibold text-[#2A3439]">
              {displayValue}
            </span>
          </div>
        );

      case "Type":
        return (
          <div key={key} className="bg-white rounded-lg p-4 mb-4">
            <span className="typography-body-sm font-weight-medium px-3 py-1 bg-gray-100 rounded-full text-gray-700">
              {displayValue}
            </span>
          </div>
        );

      case "Story Points":
      case "User Story Type":
      case "Assigned To":
        return (
          <div className="bg-white rounded-lg p-4 mb-4" key={key}>
            <div className="flex items-center justify-between mb-2">
              <span className="font-weight-semibold text-[#2A3439]">{label}</span>
              {onUpdate && (
                <BootstrapTooltip title="Edit" placement="top">
                  <button
                    onClick={() => handleEdit(key, displayValue, label)}
                    className="flex items-center gap-2 px-3 py-1.5 typography-body-sm text-red-600 bg-white hover:bg-red-50 border border-gray-200 hover:border-red-300 rounded-md transition-colors duration-200"
                  >
                    <Edit2 size={14} className="text-red-600" />
                    <span>Edit</span>
                  </button>
                </BootstrapTooltip>
              )}
            </div>
            <div className="text-[#464F60]">{displayValue}</div>
          </div>
        );

      case "Due Date":
        return (
          <div className="bg-white rounded-lg p-4 mb-4" key={key}>
            <strong className="font-weight-semibold text-[#2A3439] block mb-2">
              {label}
            </strong>
            <p className="text-[#464F60]">{formatUTCToLocal(displayValue) || "N/A"}</p>
          </div>
        );

      case "Is Architectural Leaf":
        return null;

      default:
        switch (displayType) {
          case "rich_text":
          case "text":
          case "select":
            return (
              <div className="bg-white rounded-lg mb-2" key={key}>
                <Accordion
                  title={label}
                  defaultOpen={true}
                  preview={`Open this for ${key.toLowerCase()}`}
                  type={displayType}
                  onEdit={
                    onUpdate ? () => handleEdit(key, displayValue, label) : undefined
                  }
                  mode={shouldUseAccordion(displayValue) ? "dynamic" : "static"}
                >
                  <div className="text-[#464F60]">
                    {renderContent(displayValue, isTechStack, true)}
                  </div>
                </Accordion>
              </div>
            );

          case "type_toggle":
            const relatedField = metadata[key]?.related_field;
            const relatedValue = relatedField ? properties[relatedField] : null;
            const relatedLabel = relatedField ? transformKey(relatedField) : "";
            const toggleId = `toggle_${key}`;
            const activeView = activeToggle[toggleId] || "primary";

            return (
              <div className="bg-white rounded-lg mb-2" key={key}>
                <Accordion
                  title={`${label} / ${relatedLabel}`}
                  defaultOpen={true}
                  preview={`Open to view ${key.toLowerCase()} and ${relatedField?.toLowerCase()}`}
                  type="type_toggle"
                  onEdit={
                    onUpdate ? () => handleEdit(key, displayValue, label) : undefined
                  }
                  mode={shouldUseAccordion(displayValue) ? "dynamic" : "static"}
                >
                  <div className="mb-3 flex justify-end">
                    <div className="inline-flex rounded-[4px] border border-gray-200">
                      <button
                        onClick={() => setActiveToggle({...activeToggle, [toggleId]: "primary"})}
                        className={`px-5 py-1.5 rounded-l-[4px] font-medium transition-colors duration-200
                          ${activeView === "primary"
                            ? "bg-gray-200 text-gray-900 shadow-sm"
                            : "text-gray-500 hover:text-gray-700"
                          }`}
                        style={{ outline: "none", border: "none" }}
                      >
                        {label.toLowerCase()=="entity relationship diagram" ? "ER Diagram" : label}
                      </button>
                      <button
                        onClick={() => setActiveToggle({...activeToggle, [toggleId]: "related"})}
                        className={`px-5 py-1.5 rounded-r-[4px] font-medium transition-colors duration-200
                          ${activeView === "related"
                            ? "bg-gray-200 text-gray-900 shadow-sm"
                            : " text-gray-500 hover:text-gray-700"
                          }`}
                        style={{ outline: "none", border: "none" }}
                      >
                        {relatedLabel}
                      </button>
                    </div>
                  </div>
                  
                  {activeView === "primary" && displayValue && (
                    <div className="text-[#464F60]">
                      <NoSSR chartDefinition={displayValue} />
                    </div>
                  )}
                  
                  {activeView === "related" && relatedValue && (
                    <div className="text-[#464F60]">
                      {renderContent(relatedValue, false, true)}
                    </div>
                  )}
                </Accordion>
              </div>
            );

          case "mermaid_chart":
          case "mermaid":
          case "plantuml":
            return (
              <div className="bg-white rounded-lg mb-2" key={key}>
                <Accordion title={label} defaultOpen={true}
                  onEdit={
                    onUpdate ? () => handleEdit(key, displayValue, label) : undefined
                  }
                  mode={shouldUseAccordion(displayValue) ? "dynamic" : "static"}
                >
                  {displayValue ? (
                    <NoSSR chartDefinition={displayValue} />
                  ) : (
                    <p className="text-red-500">
                      Error: No chart definition available.
                    </p>
                  )}
                </Accordion>
              </div>
            );

          case "api_doc":
            return (
              <div className="bg-white rounded-lg mb-2" key={key}>
                <Accordion title={label} defaultOpen={true}>
                  <APIDocumentation apiDetails={properties} />
                </Accordion>
              </div>
            );
          case "d2_diagram":
            return (
              <div className="bg-white rounded-lg mb-2" key={key}>
                <Accordion title={label} defaultOpen={true}
                  onEdit={
                    onUpdate ? () => handleEdit(key, displayValue, label) : undefined
                  }
                  mode={shouldUseAccordion(displayValue) ? "dynamic" : "static"}
                >
                  {displayValue ? (
                    <D2Chart chartDefinition={displayValue} />
                  ) : (
                    <p className="text-red-500">
                      Error: No chart definition available.
                    </p>
                  )}
                </Accordion>
              </div>
            );

          default:
            if (shouldUseAccordion(displayValue)) {
              return (
                <div className="bg-white rounded-lg mb-2" key={key}>
                  <Accordion
                    title={label}
                    defaultOpen={true}
                    preview={`Open this for ${key.toLowerCase()}`}
                    type={displayType}
                    onEdit={
                      onUpdate ? () => handleEdit(key, displayValue, label) : undefined
                    }
                  >
                    <div className="text-[#464F60]">
                      {renderContent(displayValue, isTechStack, true)}
                    </div>
                  </Accordion>
                </div>
              );
            }
            return (
              <div className="bg-white rounded-lg p-4 mb-4" key={key}>
                <div className="flex items-center justify-between mb-2">
                  <span className="font-weight-semibold text-[#2A3439]">{label}</span>
                  {onUpdate && (
                    <button
                      onClick={() => handleEdit(key, displayValue, label)}
                      className="flex items-center gap-2 px-3 py-1 typography-body-sm text-gray-600 hover:text-red-600 bg-white hover:bg-red-50 border  border-red-300 rounded-md transition-colors duration-200"
                    >
                      <Edit2 size={14} className="text-red-600" />
                      <span>Edit</span>
                    </button>
                  )}
                </div>
                <div className="text-[#464F60]">
                  {renderContent(displayValue, isTechStack, true)}
                </div>
              </div>
            );
        }
    }
  };

  const renderProperties = (properties: Properties, metadata: Metadata) => {
    const keysToRender = to_show.length > 0 ? to_show : Object.keys(metadata);
    return (
      <div className="space-y-4">
        {keysToRender
          .filter((key) => properties[key] !== undefined)
          .map((key) => renderField(key, properties[key]))
          .filter(Boolean)}
      </div>
    );
  };

  return (
    <div>
      {renderProperties(properties, metadata)}
      {isEditorOpen && (
        <MarkdownEditor
          isOpen={isEditorOpen}
          onClose={handleEditorClose}
          content={currentEditField.value || ""}
          onSave={async (newContent: string) => {
            if (onUpdate) {
              await onUpdate(currentEditField.key, newContent);
            }
            handleEditorClose();
          }}
          title={`Edit ${currentEditField.label}`}
          displayType={metadata[currentEditField.key]?.display_type || 'text'}
        />
      )}
    </div>
  );
};

export default PropertiesRenderer;
