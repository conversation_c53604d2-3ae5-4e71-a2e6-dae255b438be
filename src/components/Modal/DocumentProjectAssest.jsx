"use client";
import React, { useState, useRef, useEffect, useContext } from "react";
import {
  Search,
  Eye,
  Download,
  X,
  Figma,
  Code,
  FileText,
  Plus,
  RefreshCw,
  Trash2,
  Info,
  ArrowLeft,
} from "lucide-react";
import {
  extractTextFromFile,
  getProjectFiles,
  deleteProjectFile,
} from "@/utils/api";
import { usePathname } from "next/navigation";
import { AlertContext } from "../NotificationAlertService/AlertList";
import Cookies from "js-cookie";
import EmptyStateView from "./EmptyStateModal";
import Pagination from "../UIComponents/Paginations/Pagination";
import RepositoryList from "./RepoProjectAsset";
import { DynamicButton } from "@/components/UIComponents/Buttons/DynamicButton";
import { useProjectAsset } from "../Context/ProjectAssetContext";
import { getkginfo } from "@/utils/gitAPI";
import DeleteProjectModal from "../Modal/DeleteProjectModal";
import { DriverContext } from "../Context/DriverContext";
import { FileViewerModal } from "./FileViewerModal";
import { getFramesList, getFigmaDesignList } from "@/api/figma";
import FigmaImportModal from "./FigmaImportModal";

const ProjectAssets = ({ onClose, handleCloseAsset }) => {
  const [filteredDocuments, setFilteredDocuments] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [hasAssets, setHasAssets] = useState(false);
  const [documentList, setDocumentList] = useState([]);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [documentToDelete, setDocumentToDelete] = useState(null);
  const [processingDocuments, setProcessingDocuments] = useState(new Set());
  const fileInputRef = useRef(null);
  const pathname = usePathname();
  const projectId = pathname.split("/")[3];
  const { showAlert } = useContext(AlertContext);
  const author = Cookies.get("username");
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(5);
  const [searchQuery, setSearchQuery] = useState("");
  const [isCodeOpen, setIsCodeOpen] = useState(false);
  const [isRepoModalOpen, setIsRepoModalOpen] = useState(false);
  const [codeLength, setCodeLength] = useState(0);
  const [clickedOption, setClickedOption] = useState("");
  const modalRef = useRef(null);
  const { setActiveTab, activeTab } = useProjectAsset();
  const [refreshClicked, setRefreshClicked] = useState(false);
  const [tabCounts, setTabCounts] = useState({
    code: 0,
    documents: 0,
    design: 0,
  });
  const [viewerModal, setViewerModal] = useState({
    isOpen: false,
    file: null,
    viewUrl: ''
  });
  const [isFigmaModalOpen, setIsFigmaModalOpen] = useState(false);
  const [figmaLink, setFigmaLink] = useState("");
  const [designName, setDesignName] = useState("");
  const [framesList, setFramesList] = useState([]);
  const [figmaSearchQuery, setFigmaSearchQuery] = useState("");
  const [isExtractingFrames, setIsExtractingFrames] = useState(false);
  const [showExistingDesign, setShowExistingDesign] = useState(false);
  const [designList, setDesignList] = useState([]);
  const [selectedDesignId, setSelectedDesignId] = useState(null);
  const [selectedDesignName, setSelectedDesignName] = useState("");
  const [showFramesList, setShowFramesList] = useState(false);
  const { isDriverActive, prepareTempDriver } = useContext(DriverContext);
  const [isDesignLoad, setIsDesignLoad] = useState(true)

  useEffect(() => {
    if (isDriverActive) {
      prepareTempDriver('projectAsset');
    }
  }, []);

  const fetchInitialData = async () => {
    try {
      setIsLoading(true);
      // Fetch documents
      const documentsResult = await getProjectFiles(projectId);
      if (documentsResult && documentsResult.files) {
        // Use a Map to ensure uniqueness by file_uuid
        const uniqueDocsMap = new Map();

        documentsResult.files.forEach((file) => {
          const formattedDoc = {
            id: file.file_uuid || file.filename,
            name: file.filename,
            file_uuid: file.file_uuid,
            type: "PDF",
            size: formatBytes(file.size),
            author: file.uploaded_by || "Unknown",
            tags: ["document"],
            status: file.status,
            progress:
              file.percentage || (file.status === "processing" ? 0 : 100),
            percentage:
              file.percentage || (file.status === "processing" ? 0 : 100),
            viewUrl: file.view_url,
            downloadUrl: file.download_url,
            lastModified: new Date(file.last_modified).toLocaleDateString(),
            elapsedTime: file.elapsed_time || 0,
            remainingTime: file.remaining_time || "",
            isClientSide: false,
          };

          // Use file_uuid as key, fallback to filename if uuid doesn't exist
          const key = file.file_uuid || file.filename;
          uniqueDocsMap.set(key, formattedDoc);
        });

        const formattedDocuments = Array.from(uniqueDocsMap.values());

        setDocumentList((prev) => {
          if (prev.length === 0) {
            return formattedDocuments;
          }

          // Merge with existing client-side documents
          const existingClientDocs = prev.filter((doc) => doc.isClientSide);
          const serverFileNames = new Set(
            formattedDocuments.map((doc) => doc.name.toLowerCase())
          );

          // Filter out client docs that already exist on server
          const uniqueClientDocs = existingClientDocs.filter(
            (doc) => !serverFileNames.has(doc.name.toLowerCase())
          );

          return [...formattedDocuments, ...uniqueClientDocs];
        });

        setTabCounts((prev) => ({
          ...prev,
          documents: uniqueDocsMap.size,
        }));
      }

      // Fetch repositories
      const reposResult = await getkginfo(projectId, true);
      if (reposResult.details && reposResult.details.length) {
        setTabCounts((prev) => ({
          ...prev,
          code: reposResult.details.length,
        }));
      }

      // Design count remains 0 as it's coming soon
      setHasAssets(true);
    } catch (error) {

    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchInitialData();
  }, [projectId]);

  const tabs = [
    { id: "code", label: "Code", count: tabCounts.code },
    { id: "documents", label: "Documents", count: tabCounts.documents },
    { id: "design", label: "Design", count: tabCounts.design },
  ];

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (isDeleteModalOpen || viewerModal?.isOpen || isFigmaModalOpen) {
        return; // Don't close if delete modal or file viewer modal is open
      }
      if (modalRef.current && !modalRef.current.contains(event.target)) {
        onClose();
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [onClose, isDeleteModalOpen, viewerModal?.isOpen, isFigmaModalOpen]);

  // Add this useEffect
  useEffect(() => {
    if (activeTab === "design") {
      fetchDesignList();
    }
  }, [activeTab, projectId]);

  // Update how we format documents from the API response
  useEffect(() => {
    if (
      clickedOption === "Upload Documents" ||
      activeTab === "documents" ||
      refreshClicked
    ) {
      if (refreshClicked) {
        setRefreshClicked(false);
      }
      const fetchProjectFiles = async () => {
        setIsLoading(true);
        try {
          const result = await getProjectFiles(projectId);
          if (result && result.files) {
            const formattedDocuments = result.files.map((file) => ({
              id: file.filename,
              name: file.filename,
              file_uuid: file.file_uuid,
              type: "PDF",
              size: formatBytes(file.size),
              author: file.uploaded_by || "Unknown",
              tags: ["document"],
              status: file.status,
              progress: file.percentage,
              viewUrl: file.view_url,
              downloadUrl: file.download_url,
              lastModified: new Date(file.last_modified).toLocaleDateString(),
              // Add the new fields
              elapsedTime: file.elapsed_time || 0,
              remainingTime: file.remaining_time || "",
              percentage: file.percentage || 0,
            }));

            // Only set if we don't have existing documents
            setDocumentList((prev) =>
              prev.length ? prev : formattedDocuments
            );
            setTabCounts((prev) => ({
              ...prev,
              documents: formattedDocuments.length,
            }));
            setHasAssets(true);
          }
        } catch (error) {
          console.error("Failed to fetch documents:", error);
          showAlert("Failed to fetch project files", "error");
        } finally {
          setIsLoading(false);
        }
      };

      fetchProjectFiles();
    }
  }, [projectId, clickedOption, activeTab, refreshClicked]);

  useEffect(() => {
    const style = document.createElement("style");
    style.textContent = `
      @keyframes progress-pulse {
        0% { opacity: 0.6; }
        50% { opacity: 1; }
        100% { opacity: 0.6; }
      }
      
      @keyframes blink-animation {
        0% { opacity: 1; }
        50% { opacity: 0.3; }
        100% { opacity: 1; }
      }
      
      .blink-animation {
        animation: blink-animation 1.5s ease-in-out infinite;
      }
    `;
    document.head.appendChild(style);
    return () => document.head.removeChild(style);
  }, []);

  useEffect(() => {
    // Check if there are any documents in the "processing" state
    const hasProcessingDocs = documentList.some(doc => doc.status === "processing");

    if (hasProcessingDocs) {
      const refreshInterval = setInterval(async () => {
        try {
          const result = await getProjectFiles(projectId);
          if (result && result.files) {
            setDocumentList((prevList) => {
              // Create maps for efficient lookups
              const existingDocsMap = new Map();
              const fileNameToDocMap = new Map();

              // Build maps from existing documents
              prevList.forEach((doc) => {
                if (doc.file_uuid) {
                  existingDocsMap.set(doc.file_uuid, doc);
                }
                // Track by filename for deduplication
                fileNameToDocMap.set(doc.name.toLowerCase(), doc);
              });

              // Process server documents
              const processedDocs = new Map();

              result.files.forEach((serverFile) => {
                // Check if we already have this document by file_uuid or name
                const existingByUuid = existingDocsMap.get(
                  serverFile.file_uuid
                );
                const existingByName = fileNameToDocMap.get(
                  serverFile.filename.toLowerCase()
                );

                let progressPercentage = 0;
                let elapsedTime = 0;
                let remainingTime = "";

                // Extract progress data
                if (
                  serverFile.progress &&
                  typeof serverFile.progress === "object"
                ) {
                  progressPercentage = serverFile.progress.percentage || 0;
                  elapsedTime = serverFile.progress.elapsed_time || 0;

                  if (
                    serverFile.progress.timeout_value &&
                    progressPercentage > 0 &&
                    progressPercentage < 100
                  ) {
                    const estimatedTotal =
                      (elapsedTime / progressPercentage) * 100;
                    const remaining = Math.max(0, estimatedTotal - elapsedTime);
                    remainingTime = `${Math.round(remaining)}s`;
                  }
                } else if (serverFile.percentage !== undefined) {
                  progressPercentage = serverFile.percentage;
                }

                const updatedDoc = {
                  id: serverFile.file_uuid || serverFile.filename,
                  name: serverFile.filename,
                  file_uuid: serverFile.file_uuid,
                  type: "PDF",
                  size: formatBytes(serverFile.size),
                  author: serverFile.uploaded_by || "Unknown",
                  tags: ["document"],
                  status: serverFile.status,
                  progress: progressPercentage,
                  percentage: progressPercentage,
                  viewUrl: serverFile.view_url,
                  downloadUrl: serverFile.download_url,
                  lastModified: new Date(
                    serverFile.last_modified
                  ).toLocaleDateString(),
                  elapsedTime,
                  remainingTime,
                  isClientSide: false,
                };

                // Check for status change notifications
                const prevDoc = existingByUuid || existingByName;
                if (prevDoc && prevDoc.status === "processing") {
                  if (serverFile.status === "completed") {
                    showAlert(
                      `${serverFile.filename} processed successfully`,
                      "success"
                    );
                  } else if (serverFile.status === "failed") {
                    showAlert(
                      `${serverFile.filename} processing failed`,
                      "error"
                    );
                  }
                }

                // Use file_uuid as the key to prevent duplicates
                processedDocs.set(
                  serverFile.file_uuid || serverFile.filename,
                  updatedDoc
                );
              });

              // Build final document list
              const finalDocs = [];
              const processedFileNames = new Set();

              // First, add all server documents
              processedDocs.forEach((doc) => {
                finalDocs.push(doc);
                processedFileNames.add(doc.name.toLowerCase());
              });

              // Then add client-side documents that aren't duplicates
              prevList.forEach((doc) => {
                if (
                  doc.isClientSide &&
                  !processedFileNames.has(doc.name.toLowerCase())
                ) {
                  // Keep client-side documents that haven't been processed by server yet
                  finalDocs.push(doc);
                } else if (
                  doc.status === "error" &&
                  !processedFileNames.has(doc.name.toLowerCase())
                ) {
                  // Keep error documents for visibility
                  finalDocs.push(doc);
                }
              });

              return finalDocs;
            });
          }
        } catch (error) {
          console.error("Failed to refresh document status:", error);
        }
      }, 3000); // Refresh every 3 seconds

      // Clean up the interval when component unmounts
      return () => clearInterval(refreshInterval);
    }
  }, [documentList.filter((d) => d.status === "processing").length, projectId]); // Only re-run when processing count changes

  const handleAddDocument = () => {
    // Trigger click on the hidden file input
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const handleFileChange = async (event) => {
    try {
      const files = event.target.files;

      if (!files || files.length === 0) return;

      if (files.length > 6) {
        showAlert(`Maximum upload limit exceeded. You can upload up to 6 documents at a time.`, "error");
        if (fileInputRef.current) fileInputRef.current.value = '';
        return;
      }

      // Check for duplicate filenames
      const existingFileNames = new Set(
        documentList.map((doc) => doc.name.toLowerCase())
      );
      const duplicateFiles = [];
      const validFiles = [];

      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        if (existingFileNames.has(file.name.toLowerCase())) {
          duplicateFiles.push(file.name);
        } else {
          validFiles.push(file);
          existingFileNames.add(file.name.toLowerCase());
        }
      }

      // Alert about duplicates
      if (duplicateFiles.length > 0) {
        showAlert(
          `Files already exist: ${duplicateFiles.join(", ")}`,
          "warning"
        );
      }

      if (validFiles.length === 0) {
        if (fileInputRef.current) fileInputRef.current.value = "";
        return;
      }

      // Show notification if uploading multiple files
      if (validFiles.length > 1) {
        showAlert(`Uploading ${validFiles.length} documents...`, "info");
      }

      // Batch process all files and update state once
      const newDocuments = [];
      const uploadPromises = [];

      for (let i = 0; i < validFiles.length; i++) {
        const file = validFiles[i];

        // Create temporary client-side document with unique ID
        const tempDoc = {
          id: `temp_${Date.now()}_${i}`, // Temporary ID
          name: file.name,
          type: file.name.split(".").pop().toUpperCase() || "PDF",
          size: formatBytes(file.size),
          author: author || "Unknown",
          tags: ["document"],
          status: "processing",
          progress: 0,
          percentage: 0,
          isClientSide: true, // Flag to identify client-side docs
          uploadStartTime: Date.now(),
        };

        newDocuments.push(tempDoc);

        // Create upload promise
        const uploadPromise = extractTextFromFile(projectId, file)
          .then((result) => ({
            tempId: tempDoc.id,
            filename: file.name,
            file_uuid: result?.files?.[0]?.file_uuid,
            success: true,
          }))
          .catch((error) => ({
            tempId: tempDoc.id,
            filename: file.name,
            error: error.message,
            success: false,
          }));

        uploadPromises.push(uploadPromise);
      }

      // Add all new documents to state immediately
      setDocumentList((prev) => [...prev, ...newDocuments]);

      // Process uploads and update with real IDs
      const results = await Promise.allSettled(uploadPromises);

      results.forEach((result) => {
        if (result.status === "fulfilled") {
          const { tempId, filename, file_uuid, success, error } = result.value;

          setDocumentList((prevList) =>
            prevList.map((doc) => {
              if (doc.id === tempId) {
                if (success && file_uuid) {
                  return {
                    ...doc,
                    file_uuid,
                    status: "processing",
                    isClientSide: false, // Remove client-side flag once uploaded
                  };
                } else {
                  showAlert(
                    `Failed to upload ${filename}: ${error || "Unknown error"}`,
                    "error"
                  );
                  return {
                    ...doc,
                    status: "error",
                    progress: 0,
                    percentage: 0,
                  };
                }
              }
              return doc;
            })
          );
        }
      });

      // Update tab count
      setTabCounts((prev) => ({
        ...prev,
        documents: documentList.length + newDocuments.length,
      }));

      // Reset file input
      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }
    } catch (error) {
      console.error("Upload error:", error);
      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }
    }
  };
  const formatBytes = (bytes, decimals = 2) => {
    if (!bytes || bytes === 0) return "0 Bytes";
    const k = 1024;
    const dm = decimals < 0 ? 0 : decimals;
    const sizes = ["Bytes", "KB", "MB", "GB", "TB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + " " + sizes[i];
  };

  useEffect(() => {
    const style = document.createElement("style");
    style.textContent = `
      @keyframes progress-pulse {
        0% { opacity: 0.6; }
        50% { opacity: 1; }
        100% { opacity: 0.6; }
      }
    `;
    document.head.appendChild(style);
    return () => document.head.removeChild(style);
  }, []);

  const handleSearch = (query) => {
    setSearchQuery(query);

    const lowercaseQuery = query.toLowerCase();
    const filtered = documentList.filter(
      (doc) =>
        doc.name.toLowerCase().includes(lowercaseQuery) ||
        doc.type.toLowerCase().includes(lowercaseQuery) ||
        doc.size.toLowerCase().includes(lowercaseQuery) ||
        doc.author.toLowerCase().includes(lowercaseQuery) ||
        doc.tags.some((tag) => tag.toLowerCase().includes(lowercaseQuery)) ||
        (doc.status && doc.status.toLowerCase().includes(lowercaseQuery))
    );

    setFilteredDocuments(filtered);
  };

  const getCurrentPageItems = () => {
    const items = searchQuery ? filteredDocuments : documentList;
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    return items.slice(startIndex, endIndex);
  };

  const DocumentLoader = () => {
    return (
      <div className="border rounded-lg p-4 animate-pulse">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            {/* Document icon skeleton */}
            <div className="w-6 h-6 bg-gray-200 rounded"></div>

            <div>
              {/* Filename skeleton */}
              <div className="h-5 w-48 bg-gray-200 rounded mb-2"></div>

              {/* Info line skeleton */}
              <div className="flex items-center gap-2">
                <div className="h-4 w-12 bg-gray-200 rounded"></div>
                <div className="h-4 w-4 bg-gray-200 rounded"></div>
                <div className="h-4 w-16 bg-gray-200 rounded"></div>
                <div className="h-4 w-4 bg-gray-200 rounded"></div>
                <div className="h-4 w-24 bg-gray-200 rounded"></div>
                <div className="h-4 w-16 bg-gray-200 rounded"></div>
              </div>
            </div>
          </div>

          {/* Action buttons skeleton */}
          <div className="flex items-center gap-2">
            <div className="w-5 h-5 bg-gray-200 rounded"></div>
            <div className="w-5 h-5 bg-gray-200 rounded"></div>
          </div>
        </div>

        {/* Progress bar skeleton */}
        <div className="mt-3">
          <div className="w-full bg-gray-200 rounded-full h-2"></div>
        </div>
      </div>
    );
  };

  const DesignLoader = () => {
    return (
      <div className="border rounded-lg p-4 animate-pulse">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            {/* design icon skeleton */}
            <div className="w-6 h-6 bg-gray-200 rounded"></div>

            <div>
              {/* Filename skeleton */}
              <div className="h-5 w-48 bg-gray-200 rounded mb-2"></div>

              {/* Info line skeleton */}
              <div className="flex items-center gap-2">
                <div className="h-4 w-12 bg-gray-200 rounded"></div>
              </div>
            </div>
          </div>

          {/* Action buttons skeleton */}
          <div className="flex items-center gap-2">
            <div className="w-5 h-5 bg-gray-200 rounded"></div>
            <div className="w-5 h-5 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    );
  };

  const handleViewDocument = (viewUrl, filename, fileData) => {
    const fileExtension = filename.split(".").pop().toLowerCase();
    // Open PDF in new tab with PDF.js viewer
    // if (fileExtension === "pdf") {
      // const googleDocsViewerUrl = `https://docs.google.com/gview?url=${encodeURIComponent(viewUrl)}&embedded=true`;
      // const googleDocsViewerUrl = `https://mozilla.github.io/pdf.js/web/viewer.html?file=${encodeURIComponent(viewUrl)}`;

      // window.open(googleDocsViewerUrl, "_blank", "noopener,noreferrer");
    // } else {
      // Open other files in modal
      setViewerModal({
        isOpen: true,
        file: { ...fileData, name: filename },
        viewUrl: viewUrl
      });
    // }
  };
  const handleDownload = () => {
    showAlert("Document Downloaded Successfully", "success");
  };

  const handleDeleteButtonClick = (file_uuid, filename) => {
    setDocumentToDelete(file_uuid);
    setIsDeleteModalOpen(true);
  };

  const handleDeleteDocument = async () => {
    if (!documentToDelete) return;

    setIsDeleting(true);
    try {
      // Check if it's a client-side document
      const docToDelete = documentList.find(
        (doc) =>
          doc.file_uuid === documentToDelete || doc.id === documentToDelete
      );

      if (docToDelete?.isClientSide) {
        // Just remove from state if client-side only
        setDocumentList((prev) =>
          prev.filter((doc) => doc.id !== documentToDelete)
        );
        showAlert("Document removed", "success");
      } else {
        // Delete from server
        const response = await deleteProjectFile(projectId, documentToDelete);
        if (response.status === 200) {
          setDocumentList((prev) =>
            prev.filter((doc) => doc.file_uuid !== documentToDelete)
          );
          showAlert("Document deleted successfully", "success");
        } else {
          showAlert(
            response.message || "Failed to delete the document",
            "danger"
          );
        }
      }

      // Update tab count
      setTabCounts((prev) => ({
        ...prev,
        documents: Math.max(0, prev.documents - 1),
      }));
    } catch (error) {
      console.error("Delete error:", error);
      showAlert("Failed to delete the document", "error");
    } finally {
      setIsDeleting(false);
      setIsDeleteModalOpen(false);
      setDocumentToDelete(null);
    }
  };
  const fileTypes = {
    pdf: "PDF",
    png: "PNG",
    jpg: "JPEG",
    txt: "Text File",
    doc: "Word Document",
  };
  const closeViewer = () => {
    setViewerModal({
      isOpen: false,
      file: null,
      viewUrl: "",
    });
  };

  // New functions for design tab
  const handleAddFigma = () => {
    setIsFigmaModalOpen(true);
  };

  const handleCloseFigmaModal = () => {
    setIsFigmaModalOpen(false);
    setFigmaLink("");
    setDesignName("");
  };

  const handleFigmaSubmit = async (accessLevel = "public") => {
    if (!figmaLink.trim()) return;

    setIsExtractingFrames(true);
    try {
      const figmaData = {
        name: designName || "Figma Design",
        url: figmaLink,
        accessLevel: accessLevel, // Include access level in the request
      };

      const extractedResponse = await getFramesList(projectId, figmaData);

      if (extractedResponse?.screen_list) {
        setFramesList(extractedResponse.screen_list);
        showAlert("Figma Designs extracted successfully", "success");
        setIsFigmaModalOpen(false);
        setFigmaLink("");
        setDesignName("");

        // Refresh design list and show designs first
        await fetchDesignList();
        setShowFramesList(false);
      }
    } catch (error) {
      console.error("Figma extraction error:", error);
      showAlert("Failed to extract Figma design", "error");
    } finally {
      setIsExtractingFrames(false);
    }
  };

  const handleBackToDesign = () => {
    setShowExistingDesign(false);
  };

  const handleFigmaSearch = (query) => {
    setFigmaSearchQuery(query);
  };

  const getFilteredFrames = () => {
    if (!figmaSearchQuery) return framesList;
    return framesList.filter(
      (frame) =>
        frame.screen_name
          .toLowerCase()
          .includes(figmaSearchQuery.toLowerCase()) ||
        frame.Canvas.toLowerCase().includes(figmaSearchQuery.toLowerCase())
    );
  };

  // Add these new functions
  const fetchDesignList = async () => {
    try {
      setIsDesignLoad(true);
      const response = await getFigmaDesignList(projectId);
      if (response?.data.length > 0) {
        setDesignList(response.data);
        setTabCounts((prev) => ({
          ...prev,
          design: response.data.length,
        }));
      }
    } catch (error) {
      console.error("Failed to fetch design list:", error);
    } finally {
      setIsDesignLoad(false);
    }
  };

  const handleDesignClick = async (designId, designName) => {
    try {
      setSelectedDesignId(designId);
      setSelectedDesignName(designName);
      const response = await getFigmaDesignList(projectId, designId);
      if (response?.data?.screen_list) {
        setFramesList(response?.data?.screen_list);
        setShowFramesList(true);
      }
    } catch (error) {
      console.error("Failed to fetch frames:", error);
      showAlert("Failed to load design frames", "error");
    }
  };

  const handleBackToDesigns = () => {
    setShowFramesList(false);
    setSelectedDesignId(null);
    setSelectedDesignName("");
    setFramesList([]);
  };

  const handleRefresh = async () => {
    setRefreshClicked(true);
    try {
      const result = await getProjectFiles(projectId);
      if (result && result.files) {
        // Build unique documents map
        const uniqueDocsMap = new Map();

        result.files.forEach((file) => {
          const formattedDoc = {
            id: file.file_uuid || file.filename,
            name: file.filename,
            file_uuid: file.file_uuid,
            type: "PDF",
            size: formatBytes(file.size),
            author: file.uploaded_by || "Unknown",
            tags: ["document"],
            status: file.status,
            progress: file.percentage || 0,
            percentage: file.percentage || 0,
            viewUrl: file.view_url,
            downloadUrl: file.download_url,
            lastModified: new Date(file.last_modified).toLocaleDateString(),
            elapsedTime: file.elapsed_time || 0,
            remainingTime: file.remaining_time || "",
            isClientSide: false,
          };

          uniqueDocsMap.set(file.file_uuid || file.filename, formattedDoc);
        });

        setDocumentList((prevList) => {
          // Create map of existing client-side documents
          const clientDocs = prevList.filter((doc) => doc.isClientSide);

          // Merge client-side docs with server docs
          return [...clientDocs, ...Array.from(uniqueDocsMap.values())];
        });

        setTabCounts((prev) => ({
          ...prev,
          documents: uniqueDocsMap.size,
        }));

        showAlert("Documents refreshed", "success");
      }
    } catch (error) {
      console.error("Refresh failed:", error);
      showAlert("Failed to refresh documents", "error");
    } finally {
      setRefreshClicked(false);
    }
  };

  return (
    <div>
      <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center">
        <div
          ref={modalRef}
          className="w-full min-w-[300px] max-w-[60vw] h-[88vh] bg-white rounded-lg shadow relative z-50 flex flex-col overflow-hidden"
          id="projectAssetModal"
        >
          {/* Header */}
          <div className="flex items-center justify-between px-4 py-2 border-b">
            <h2 className="typography-body-lg font-weight-medium">
              Project Assets
            </h2>
            <div className="flex gap-2">
              {/* <button className="p-1 hover:bg-gray-100 rounded">
                <Image
                  src={kebabicon}
                  alt="kebab Icon"
                  width={20}
                  height={20}
                />
              </button>
             
              <button className="p-1 hover:bg-gray-100 rounded">
                <Maximize2 className="w-5 h-5 text-gray-500" />
              </button> */}
              {/* X (close) icon */}
              <button
                className="p-1 hover:bg-gray-100 rounded"
                title="click to close"
                onClick={onClose}
              >
                <X className="w-5 h-5 text-gray-500" />
              </button>
            </div>
          </div>



          <>
            <div className="flex gap-6 mt-1 px-4 py-1">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => {
                    setIsRepoModalOpen(false);
                    setActiveTab(tab.id);
                    // Reset isCodeOpen when switching tabs
                    if (tab.id !== "code") {
                      setIsCodeOpen(false);
                    }
                  }}
                  className={`flex items-center gap-2 pb-2 ${activeTab === tab.id
                    ? "border-b-2 border-primary text-primary"
                    : "text-gray-600"
                    }`}
                >
                  {tab.id === "code" && <Code className="w-5 h-5" />}
                  {tab.id === "documents" && <FileText className="w-5 h-5" />}
                  {tab.id === "design" && <Figma className="w-5 h-5" />}
                  {tab.label} ({tab.count})
                </button>
              ))}
            </div>
            {activeTab === "documents" && (
              <div className="flex flex-col h-[calc(100%-80px)]">
                {/* Search and Add Document section */}
                <div className="px-4 py-4 flex items-center justify-between gap-4">
                  <div className="relative flex-1">
                    <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                    <input
                      type="text"
                      placeholder="Search documents."
                      className="w-full pl-12 pr-8 py-1 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                      value={searchQuery}
                      onChange={(e) => handleSearch(e.target.value)}
                    />
                  </div>

                  <DynamicButton
                    variant="primary"
                    icon={RefreshCw}
                    onClick={handleRefresh}
                    tooltip="Refresh"
                  />

                  <DynamicButton
                    variant="primary"
                    icon={Plus}
                    text="Add Documents"
                    onClick={handleAddDocument}
                    tooltip="Add Documents"
                  />
                  {/* <button
                    className="px-4 py-2 bg-primary text-white rounded-lg flex items-center gap-2 hover:bg-primary-600"
                    onClick={handleAddDocument}
                  >
                    <span className="typography-heading-4">+</span> Add Document
                  </button> */}
                  <input
                    type="file"
                    ref={fileInputRef}
                    accept=".pdf,.doc,.docx,.xls,.xlsx,.png,.jpg,.jpeg,.gif"
                    multiple
                    className="hidden"
                    onChange={handleFileChange}
                  />
                </div>

                {/* Document list with empty states */}
                <div className="flex-1 overflow-y-auto px-4">
                  {isLoading ? (
                    <div className="space-y-4 py-4">
                      <DocumentLoader />
                      <DocumentLoader />
                      <DocumentLoader />
                    </div>
                  ) : documentList.length === 0 ? (
                    <EmptyStateView
                      type="noDocumentsFound"
                      onClick={handleAddDocument}
                    />
                  ) : searchQuery && filteredDocuments.length === 0 ? (
                    <EmptyStateView
                      type="noDocumentSearchResults"
                      onClick={() => handleSearch("")}
                    />
                  ) : (
                    <div className="space-y-4 py-4">
                      {getCurrentPageItems().map((doc) => (
                        <div key={doc.id}>
                          {/* Document entry with better percentage display */}
                          <div className="border rounded-lg p-4">
                            {/* Document header with title and status */}
                            <div className="flex items-center justify-between">
                              {/* Document icon and metadata */}
                              <div className="flex items-center gap-3">
                                <svg
                                  viewBox="0 0 24 24"
                                  className="w-6 h-6 text-primary -mt-4"
                                >
                                  <path
                                    fill="currentColor"
                                    d="M14 2H6a2 2 0 00-2 2v16a2 2 0 002 2h12a2 2 0 002-2V8l-6-6zm-1 1v5h5v10H6V4h7z"
                                  />
                                </svg>
                                <div>
                                  <div className="flex gap-3 items-center">
                                    <h3 className="font-weight-medium">{doc.name}</h3>
                                    <div
                                      className={`inline-block bg-gray-100 text-gray-600 typography-body-sm px-2 py-0.5 rounded mr-2 ${doc.status === "processing"
                                        ? "bg-primary-100 text-primary"
                                        : doc.status === "completed"
                                          ? "bg-green-100 text-green-600"
                                          : doc.status === "failed"
                                            ? "bg-red-100 text-red-600"
                                            : "invisible"
                                        }`}
                                    >
                                      {doc.status}
                                    </div>

                                    {doc.tags.map((tag) => (
                                      <span
                                        key={tag}
                                        className="inline-block bg-gray-100 text-gray-600 typography-body-sm px-2 py-0.5 rounded -ml-1"
                                      >
                                        {tag}
                                      </span>
                                    ))}
                                  </div>

                                  {/* Document metadata */}
                                  <div className="flex items-center gap-2 typography-body-sm text-gray-500 mt-2">
                                    <span>
                                      {" "}
                                      {fileTypes[
                                        doc.name
                                          ?.split(".")
                                          .pop()
                                          ?.toLowerCase()
                                      ] ||
                                        doc.name
                                          ?.split(".")
                                          .pop()
                                          ?.toUpperCase() ||
                                        "Unknown"}
                                    </span>
                                    <span>•</span>
                                    <span>{doc.size}</span>
                                    <span>•</span>
                                    <span>By {doc.author}</span>
                                    <span>•</span>
                                    <span>Modified {doc.lastModified}</span>
                                  </div>
                                </div>
                              </div>

                              {/* Action buttons */}
                              <div className="flex items-center gap-4">
                                <button
                                  onClick={() =>
                                    handleViewDocument(doc.viewUrl, doc.name)
                                  }
                                  title="View document"
                                  className="flex items-center gap-1"
                                >
                                  <Eye className="w-5 h-5 text-gray-400 hover:text-gray-600 cursor-pointer" />
                                </button>
                                <a
                                  href={doc.downloadUrl}
                                  download
                                  title="Download document"
                                  className="flex items-center gap-1"
                                  onClick={handleDownload}
                                >
                                  <Download className="w-5 h-5 text-gray-400 hover:text-gray-600 cursor-pointer" />
                                </a>
                              <button
                                  onClick={() =>
                                    handleDeleteButtonClick(doc.file_uuid, doc.name)
                                  }
                                  title="Delete document"
                                  className={`flex items-center gap-1 ${doc.status !== "completed" && doc.status !== "failed"  && doc.status !== "processing"
                                      ? "opacity-50 cursor-not-allowed"
                                      : ""
                                    }`}
                                  disabled={doc.status !== "completed" && doc.status !== "failed" && doc.status !== "processing"}
                                >
                                  <Trash2
                                    className={`w-5 h-5 ${doc.status === "completed" || doc.status === "failed" || doc.status === "processing"
                                        ? "text-gray-400 hover:text-red-600 cursor-pointer"
                                        : "text-gray-300 cursor-not-allowed"
                                      }`}
                                  />
                                </button>
                              </div>
                            </div>

                            {/* Progress display */}
                            {doc.status && (
                              <>
                                {doc.status === "processing" && (
                                  <div className="mt-2 relative">
                                    {/* Display percentage at the top right of the progress bar */}
                                    <div className="flex justify-between items-center mb-1">
                                      <span className="typography-body-sm text-white">
                                        Processing...
                                      </span>
                                      <span className="typography-body-sm font-weight-medium text-primary">
                                        {Math.round(
                                          doc.percentage || doc.progress
                                        )}
                                        %
                                      </span>
                                    </div>
                                    <div className="w-full bg-gray-200 rounded-full h-2">
                                      <div
                                        className="h-2 rounded-full bg-primary"
                                        style={{
                                          width: `${doc.percentage || doc.progress
                                            }%`,
                                          animation:
                                            "progress-pulse 1.5s ease-in-out infinite",
                                        }}
                                      />
                                    </div>
                                    {/* Add blinking effect for remaining time */}
                                    {doc.remainingTime && (
                                      <div className="mt-1 text-right">
                                        <span className="typography-caption text-gray-600 blink-animation">
                                          Estimated time remaining:{" "}
                                          {doc.remainingTime}
                                        </span>
                                      </div>
                                    )}
                                  </div>
                                )}
                                {doc.status !== "processing" && (
                                  <div className="mt-3">
                                    <div className="w-full bg-gray-200 rounded-full h-2">
                                      <div
                                        className={`h-2 rounded-full ${doc.status === "completed"
                                          ? "bg-green-500"
                                          : "bg-red-500"
                                          }`}
                                        style={{ width: "100%" }}
                                      />
                                    </div>
                                  </div>
                                )}
                              </>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>

                {/* Show pagination only when there are documents */}
                {documentList.length > 0 && (
                  <div className="">
                    <div className="px-4 py-3">
                      <Pagination
                        currentPage={currentPage}
                        pageCount={Math.ceil(
                          (searchQuery
                            ? filteredDocuments.length
                            : documentList.length) / pageSize
                        )}
                        pageSize={pageSize}
                        totalItems={
                          searchQuery
                            ? filteredDocuments.length
                            : documentList.length
                        }
                        onPageChange={setCurrentPage}
                        onPageSizeChange={setPageSize}
                        pageSizeOptions={[5, 10, 15, 20]}
                      />
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* UPDATED DESIGN TAB */}
            {activeTab === "design" && (
              <div className="flex flex-col h-[calc(100%-80px)]">
                <div className="mx-4 mb-4 bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <div className="flex items-start gap-3">
                    <Info className="w-5 h-5 text-blue-500 mt-0.5 flex-shrink-0" />
                    <div className="flex-1">
                      <p className="text-blue-800 text-sm leading-5">
                        Here you can browse your Figma designs and their screens. In the <strong>Code</strong> tab, you&apos;ll have the option to extract screens and generate code based on your selected designs.
                      </p>
                    </div>
                  </div>
                </div>
                {!showFramesList ? (
                  // Design List View
                  <>
                    {/* Search and Add Figma section */}
                    <div className="px-4 py-4 flex items-center justify-between gap-4">
                      <div className="relative flex-1">
                        <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                        <input
                          type="text"
                          placeholder="Search designs..."
                          className="w-full pl-12 pr-8 py-1 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                          value={figmaSearchQuery}
                          onChange={(e) => handleFigmaSearch(e.target.value)}
                        />
                      </div>

                      <DynamicButton
                        variant="primary"
                        icon={Plus}
                        text="Add Figma"
                        onClick={handleAddFigma}
                        tooltip="Add Figma Design"
                      />
                    </div>

                    {/* Design list */}
                    <div className="flex-1 overflow-y-auto px-4">
                      {isDesignLoad ? (
                        <>
                          <DesignLoader />
                          <DesignLoader />
                          <DesignLoader />
                        </>
                      ) : designList.length === 0 ? (
                        // No designs at all
                        <EmptyStateView
                          type="noDesigns"
                          onClick={handleAddFigma}
                        />
                      ) : figmaSearchQuery &&
                        designList.filter((design) =>
                          design.design_name
                            .toLowerCase()
                            .includes(figmaSearchQuery.toLowerCase())
                        ).length === 0 ? (
                        // Has designs but search found nothing
                        <EmptyStateView
                          type="noSearchResult"
                          onClick={() => handleFigmaSearch("")}
                        />
                      ) : (
                        // Show filtered designs
                        <div className="space-y-4 py-4">
                          {designList
                            .filter(
                              (design) =>
                                !figmaSearchQuery ||
                                design.design_name
                                  .toLowerCase()
                                  .includes(figmaSearchQuery.toLowerCase())
                            )
                            .map((design) => (
                              <div
                                key={design.figma_id}
                                className="border border-gray-200 rounded-lg p-4 cursor-pointer transition-all duration-200 hover:bg-blue-50 hover:border-blue-400 hover:shadow-md group"
                                onClick={() => handleDesignClick(design.figma_id, design.design_name)}
                              >
                                <div className="flex items-center justify-between">
                                  <div className="flex items-center gap-3">
                                    <div className="p-2 rounded-lg bg-purple-100 group-hover:bg-purple-200 transition-colors duration-200">
                                      <Figma className="w-6 h-6 text-purple-600" />
                                    </div>
                                    <div>
                                      <h3 className="font-weight-medium text-gray-900 group-hover:text-blue-700 transition-colors duration-200">{design.design_name}</h3>
                                      <div className="flex items-center gap-2 typography-body-sm text-gray-500 mt-1">
                                        <span>Figma Design</span>
                                      </div>
                                    </div>
                                  </div>
                                  <div className="flex items-center text-gray-400 group-hover:text-blue-600 transition-all duration-200">
                                    <span className="text-sm mr-1 group-hover:mr-2 transition-all duration-200">Click to view</span>
                                    <span className="text-lg group-hover:translate-x-1 transition-transform duration-200">→</span>
                                  </div>
                                </div>
                              </div>
                            ))}
                          </div>
                        )}
                    </div>
                  </>
                ) : (
                  // Frames List View
                  <>
                    {/* Back button and search */}
                    <div className="px-4 py-4">
                      <div className="flex items-center gap-4 mb-4">
                        <DynamicButton
                          variant="primary"
                          icon={ArrowLeft}
                          text="Back"
                          onClick={handleBackToDesigns}
                          tooltip="Back to design page"
                        />
                        <h3 className="font-weight-medium text-gray-900">{selectedDesignName}</h3>
                      </div>

                      <div className="relative">
                        <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                        <input
                          type="text"
                          placeholder="Search screens..."
                          className="w-full pl-12 pr-8 py-1 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                          value={figmaSearchQuery}
                          onChange={(e) => handleFigmaSearch(e.target.value)}
                        />
                      </div>
                    </div>

                    {/* Frames list */}
                    <div className="flex-1 overflow-y-auto px-4">
                      {framesList.length === 0 ? (
                        // No frames at all
                        <EmptyStateView
                          type="noScreens"
                        />
                      ) : figmaSearchQuery && getFilteredFrames().length === 0 ? (
                        // Has frames but search found nothing
                        <EmptyStateView
                          type="noSearchResult"
                          onClick={() => handleFigmaSearch("")}
                        />
                      ) : (
                        // Show filtered frames
                        <div className="space-y-4 py-4">
                          {getFilteredFrames().map((frame) => (
                            <div key={frame.screen_id} className="border rounded-lg p-4">
                              <div className="flex items-center justify-between">
                                <div className="flex items-center gap-3">
                                  <Figma className="w-6 h-6 text-purple-600" />
                                  <div>
                                    <div className="flex gap-3 items-center">
                                      <h3 className="font-weight-medium">{frame.screen_name}</h3>
                                      {frame.processed && (
                                        <span className="inline-block bg-green-100 text-green-600 typography-body-sm px-2 py-0.5 rounded">
                                          Already Extracted
                                        </span>
                                      )}
                                    </div>
                                    {/* <div className="flex items-center gap-2 typography-body-sm text-gray-500 mt-1">
                          <span>{frame.Canvas}</span>
                        </div> */}
                                  </div>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  </>
                )}
              </div>
            )}

            {(activeTab === "code" || isCodeOpen === true) && (
              <RepositoryList
                isRepoModalOpen={isRepoModalOpen}
                setIsRepoModalOpen={setIsRepoModalOpen}
                setCodeLength={setCodeLength}
                setActiveTab={setActiveTab}
                setTabCounts={setTabCounts}
                handleCloseAsset={handleCloseAsset}
              />
            )}
          </>
        </div>
      </div>
      <DeleteProjectModal
        isOpen={isDeleteModalOpen}
        onClose={() => {
          setIsDeleteModalOpen(false);
          setDocumentToDelete(null);
        }}
        onDelete={handleDeleteDocument}
        isDeleting={isDeleting}
        type="document"
      />
      {
        viewerModal?.isOpen &&
        <FileViewerModal
          isOpen={viewerModal.isOpen}
          onClose={closeViewer}
          file={viewerModal.file}
          viewUrl={viewerModal.viewUrl}
        />
      }
      {/* Figma Modal */}
      {isFigmaModalOpen && (
        <FigmaImportModal
          isOpen={isFigmaModalOpen}
          onClose={handleCloseFigmaModal}
          onSubmit={handleFigmaSubmit}
          figmaLink={figmaLink}
          setFigmaLink={setFigmaLink}
          designName={designName}
          setDesignName={setDesignName}
          isExtractingFrames={isExtractingFrames}
          showExistingDesign={showExistingDesign}
          handleBackToDesign={handleBackToDesign}
        />
      )}
    </div>
  );
};

export default ProjectAssets;
