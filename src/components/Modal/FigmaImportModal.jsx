"use client";
import React, { useState, useEffect } from "react";
import { X } from "lucide-react";
import { initiateFigmaOAuth } from "@/utils/FigmaAPI";
import Cookies from 'js-cookie';

const FigmaImportModal = ({
  isOpen,
  onClose,
  onSubmit,
  figmaLink,
  setFigmaLink,
  designName,
  setDesignName,
  isExtractingFrames,
  showExistingDesign,
  handleBackToDesign,
  // Additional props for complete flow
  showProcessing,
  setShowProcessing,
  showFrameSelector,
  setShowFrameSelector,
  framesList,
  setFramesList,
  selectedFrames,
  setSelectedFrames,
  extractedFrames,
  setExtractedFrames,
  onExtractFrames,
  isProcessing
}) => {
  const [accessLevel, setAccessLevel] = useState("public");
  const [isConnecting, setIsConnecting] = useState(false);
  const [isConnected, setIsConnected] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [filteredFrames, setFilteredFrames] = useState([]);

  // Check if user is already connected to Figma on component mount
  useEffect(() => {
    const figmaUserData = localStorage.getItem('figmaUserData');
    if (figmaUserData) {
      setIsConnected(true);
      setAccessLevel("private");
    }
  }, []);

  // Filter frames based on search term
  useEffect(() => {
    if (!framesList) {
      setFilteredFrames([]);
      return;
    }

    if (!searchTerm.trim()) {
      setFilteredFrames(framesList);
    } else {
      const filtered = framesList.filter(frame =>
        frame?.screen_name?.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setFilteredFrames(filtered);
    }
  }, [framesList, searchTerm]);

  const handleOAuthConnect = async () => {
    setIsConnecting(true);
    try {
      const userId = Cookies.get('userId');
      const tenantId = Cookies.get('tenant_id');

      if (!userId || !tenantId) {
        throw new Error('User not authenticated');
      }

      // Get OAuth URL from backend
      const oauthResponse = await initiateFigmaOAuth(userId, tenantId);

      if (oauthResponse.url) {
        // Open OAuth popup
        const popup = window.open(
          oauthResponse.url,
          'figma-oauth',
          'width=600,height=700,scrollbars=yes,resizable=yes'
        );

        if (!popup) {
          // Popup was blocked
          setIsConnecting(false);
          alert('Popup was blocked. Please allow popups for this site and try again.');
          return;
        }

        // Listen for OAuth completion
        const handleMessage = (event) => {
          if (event.data === 'figma_connected') {
            setIsConnected(true);
            setIsConnecting(false);
            setAccessLevel("private");
            if (popup && !popup.closed) {
              popup.close();
            }
            window.removeEventListener('message', handleMessage);
            if (checkClosed) {
              clearInterval(checkClosed);
            }
          }
        };

        window.addEventListener('message', handleMessage);

        // Handle popup closed manually
        const checkClosed = setInterval(() => {
          if (popup && popup.closed) {
            clearInterval(checkClosed);
            setIsConnecting(false);
            window.removeEventListener('message', handleMessage);
          }
        }, 1000);
      }
    } catch (error) {
      console.error("OAuth connection failed:", error);
      setIsConnecting(false);
    }
  };

  const handleDisconnect = () => {
    setIsConnected(false);
    setAccessLevel("public");
  };

  // Frame selection handlers
  const handleFrameToggle = (frameId) => {
    if (extractedFrames && extractedFrames.has(frameId)) {
      return; // Do nothing if frame is already extracted
    }

    const newSelected = new Set(selectedFrames);
    if (newSelected.has(frameId)) {
      newSelected.delete(frameId);
    } else {
      newSelected.add(frameId);
    }
    setSelectedFrames(newSelected);
  };

  const handleSelectAll = () => {
    const availableFrames = filteredFrames.filter(frame =>
      !extractedFrames || !extractedFrames.has(frame.screen_id)
    );

    const selectedFromAvailable = availableFrames.filter(frame =>
      selectedFrames && selectedFrames.has(frame.screen_id)
    );

    if (selectedFromAvailable.length === availableFrames.length && availableFrames.length > 0) {
      // Deselect all
      const newSelected = new Set(selectedFrames);
      availableFrames.forEach(frame => {
        newSelected.delete(frame.screen_id);
      });
      setSelectedFrames(newSelected);
    } else {
      // Select all available
      const newSelected = new Set(selectedFrames);
      availableFrames.forEach(frame => {
        newSelected.add(frame.screen_id);
      });
      setSelectedFrames(newSelected);
    }
  };

  const handleClearSearch = () => {
    setSearchTerm("");
  };

  const handleBackToForm = () => {
    setShowProcessing(false);
    setShowFrameSelector(false);
    setSearchTerm("");
  };

  const handleSubmit = async () => {
    // Show processing screen
    setShowProcessing(true);

    // Call the parent's onSubmit function with access level and URL
    await onSubmit(accessLevel, figmaLink);

    // After processing is complete, show frame selector
    // (This will be controlled by the parent component)
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-[60] flex items-center justify-center">
      <div className={`w-full bg-white rounded-lg shadow-xl ${
        showFrameSelector ? 'max-w-2xl' : 'max-w-md'
      }`}>
        {/* Gradient Header Section */}
        <div className="relative bg-gradient-to-r from-cyan-400 via-blue-400 to-pink-400 pt-6 pb-8">
          {/* Figma Icon */}
          <div className="flex justify-center mb-4">
            <div className="w-12 h-12 rounded-xl flex items-center justify-center shadow-lg border-3 border-white bg-white">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 32 32" fill="none">
                <rect width="32" height="32" rx="8" fill="url(#figmaGradient)" />
                <defs>
                  <linearGradient id="figmaGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" stopColor="#FF7262" />
                    <stop offset="25%" stopColor="#F24E1E" />
                    <stop offset="50%" stopColor="#A259FF" />
                    <stop offset="75%" stopColor="#1ABCFE" />
                    <stop offset="100%" stopColor="#0ACF83" />
                  </linearGradient>
                </defs>
              </svg>
            </div>
          </div>

          {/* Title and subtitle */}
          <div className="text-center text-white">
            <h2 className="text-lg font-semibold mb-1">Import Figma Design</h2>
            <p className="text-sm opacity-90">Paste any Figma file URL</p>
          </div>
        </div>

        {/* Content Section - Conditional Rendering */}
        {showProcessing ? (
          // Processing State
          <div className="px-6 pb-6 text-center">
            <h2 className="text-xl font-semibold text-gray-900 mb-2">Processing</h2>
            <p className="text-gray-600 mb-8">We are fetching the screens.</p>

            {/* Processing Animation */}
            <div className="flex items-center justify-center gap-4 mb-8">
              <div className="w-12 h-12 bg-black rounded-lg flex items-center justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 32 32" fill="none">
                  <rect width="32" height="32" rx="8" fill="url(#figmaGradient)" />
                  <defs>
                    <linearGradient id="figmaGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                      <stop offset="0%" stopColor="#FF7262" />
                      <stop offset="25%" stopColor="#F24E1E" />
                      <stop offset="50%" stopColor="#A259FF" />
                      <stop offset="75%" stopColor="#1ABCFE" />
                      <stop offset="100%" stopColor="#0ACF83" />
                    </linearGradient>
                  </defs>
                </svg>
              </div>

              {/* Animated dots */}
              <div className="flex gap-1">
                <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse" style={{animationDelay: '0.2s'}}></div>
                <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse" style={{animationDelay: '0.4s'}}></div>
              </div>

              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
            </div>
          </div>
        ) : showFrameSelector ? (
          // Frame Selection State
          <div className="px-6 pb-6">
            <div className="flex items-center justify-between mb-4 mt-3">
              <h2 className="text-xl font-semibold text-gray-900">Select Screens to Extract</h2>
              <div className="flex items-center gap-2">
                <button
                  onClick={handleSelectAll}
                  className="text-sm font-medium text-orange-600 hover:text-orange-700 bg-orange-50 hover:bg-orange-100 px-3 py-1 rounded-md transition-colors"
                >
                  Select All
                </button>
                <button
                  onClick={handleBackToForm}
                  className="flex items-center gap-1 border border-orange-500 text-orange-500 hover:bg-orange-50 rounded px-3 py-1.5 text-sm font-medium"
                >
                  + Add Figma
                </button>
              </div>
            </div>

            {/* Search Bar */}
            <div className="mb-4">
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <svg className="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </div>
                <input
                  type="text"
                  placeholder="Search screens by name..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-10 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent text-sm bg-white shadow-sm"
                />
                {searchTerm && (
                  <button
                    onClick={handleClearSearch}
                    className="absolute inset-y-0 right-0 pr-3 flex items-center"
                  >
                    <X className="h-4 w-4 text-gray-400 hover:text-gray-600" />
                  </button>
                )}
              </div>
            </div>

            {/* Frames List */}
            <div className="h-64 border border-gray-200 rounded-lg overflow-y-auto bg-white shadow-sm mb-4">
              {filteredFrames && filteredFrames.length > 0 ? (
                <div className="p-4 space-y-2">
                  {filteredFrames.map((frame, index) => {
                    const isSelected = selectedFrames && selectedFrames.has(frame.screen_id);
                    const isExtracted = extractedFrames && extractedFrames.has(frame.screen_id);

                    return (
                      <div
                        key={frame.screen_id}
                        className={`flex items-center justify-between p-3 rounded-lg border transition-all duration-200 ${
                          isExtracted
                            ? 'bg-green-50 border-green-200 cursor-not-allowed'
                            : isSelected
                              ? 'bg-orange-50 border-orange-200 cursor-pointer shadow-sm'
                              : 'bg-white border-gray-200 hover:bg-gray-50 hover:border-gray-300 cursor-pointer'
                        }`}
                        onClick={() => !isExtracted && handleFrameToggle(frame.screen_id)}
                      >
                        <div className="flex items-center gap-3">
                          <div
                            className={`w-5 h-5 rounded border-2 flex items-center justify-center transition-all duration-200 ${
                              isExtracted
                                ? 'bg-green-100 border-green-400'
                                : isSelected
                                  ? 'bg-orange-500 border-orange-500 shadow-sm'
                                  : 'border-gray-300 hover:border-orange-400'
                            }`}
                          >
                            {(isExtracted || isSelected) && (
                              <svg
                                className={`w-3 h-3 ${isExtracted ? 'text-green-600' : 'text-white'}`}
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                              >
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth={3}
                                  d="M5 13l4 4L19 7"
                                />
                              </svg>
                            )}
                          </div>
                          <div className="flex flex-col">
                            <span className="text-sm font-medium text-gray-900">
                              {frame?.screen_name || `Screen ${index + 1}`}
                            </span>
                            {isExtracted && (
                              <span className="text-xs text-green-600">Already extracted</span>
                            )}
                          </div>
                        </div>
                        <div className="flex items-center">
                          <div className={`w-2 h-2 rounded-full ${
                            isExtracted ? 'bg-green-500' : isSelected ? 'bg-orange-500' : 'bg-gray-300'
                          }`}></div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              ) : (
                <div className="flex flex-col items-center justify-center h-full text-gray-500 p-6">
                  <svg className="w-12 h-12 mb-3 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                  <p className="text-sm font-medium">No screens available</p>
                  <p className="text-xs text-gray-400 mt-1">There are no screens available for the uploaded Figma design.</p>
                </div>
              )}
            </div>

            {/* Extract Button */}
            <button
              onClick={onExtractFrames}
              disabled={!selectedFrames || selectedFrames.size === 0 || isExtractingFrames}
              className={`w-full py-3 px-6 rounded-lg font-medium text-base transition-all flex items-center justify-center gap-3 ${
                !selectedFrames || selectedFrames.size === 0 || isExtractingFrames
                  ? 'bg-gray-200 text-gray-400 cursor-not-allowed'
                  : 'bg-orange-500 hover:bg-orange-600 text-white shadow-md hover:shadow-lg'
              }`}
            >
              {isExtractingFrames ? (
                <>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  <span>Extracting screens...</span>
                </>
              ) : (
                <>
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10" />
                  </svg>
                  <span>Extract Selected Screens ({selectedFrames ? selectedFrames.size : 0})</span>
                </>
              )}
            </button>
          </div>
        ) : (
          // Initial Form State
          <div className="px-6 pb-6">
            {showExistingDesign && (
              <div className="text-center mb-6">
                <button
                  onClick={handleBackToDesign}
                  className="inline-flex items-center gap-2 text-sm text-blue-600 hover:text-blue-700 font-medium transition-colors"
                >
                  <span>{'View Existing Designs'}</span>
                  <span className="text-xs">→</span>
                </button>
              </div>
            )}

            {/* Access Level Section */}
            <div className="mb-6">
              <h3 className="text-sm font-medium text-gray-900 m-3">Access Level</h3>
              <div className="grid grid-cols-2 gap-3">
                {/* Public Only Option */}
                <button
                  onClick={() => setAccessLevel("public")}
                  className={`p-4 rounded-lg border-2 text-left transition-all ${
                    accessLevel === "public"
                      ? "border-blue-500 bg-blue-50 shadow-sm"
                      : "border-gray-200 hover:border-gray-300 hover:bg-gray-50"
                  }`}
                >
                  <div className="font-semibold text-gray-900 mb-1">Public Only</div>
                  <div className="text-xs text-gray-500">No login required</div>
                </button>

                {/* Public + Private Option */}
                <button
                  onClick={() => setAccessLevel("private")}
                  className={`p-4 rounded-lg border-2 text-left transition-all ${
                    accessLevel === "private"
                      ? "border-blue-500 bg-blue-50 shadow-sm"
                      : "border-gray-200 hover:border-gray-300 hover:bg-gray-50"
                  }`}
                >
                  <div className="font-semibold text-gray-900 mb-1">Public + Private</div>
                  <div className="text-xs text-gray-500">OAuth required</div>
                </button>
              </div>
            </div>

            {/* OAuth Connection Button (shown when Private is selected) */}
            {accessLevel === "private" && (
              <div className="mb-6">
                {isConnected ? (
                  <div className="space-y-3">
                    <div className="flex items-center justify-center gap-2 bg-green-50 border border-green-200 rounded-lg p-4">
                      <div className="w-5 h-5 bg-green-500 rounded-full flex items-center justify-center">
                        <svg className="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                      </div>
                      <span className="text-green-700 font-medium">Connected to Figma</span>
                    </div>
                    <button
                      onClick={handleDisconnect}
                      className="w-full bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium py-2 px-4 rounded-lg transition-colors text-sm"
                    >
                      Disconnect
                    </button>
                  </div>
                ) : (
                  <button
                    onClick={handleOAuthConnect}
                    disabled={isConnecting}
                    className={`w-full py-3 px-4 rounded-lg font-medium transition-all ${
                      isConnecting
                        ? "bg-blue-300 cursor-not-allowed"
                        : "bg-blue-500 hover:bg-blue-600"
                    } text-white flex items-center justify-center gap-2`}
                  >
                    {isConnecting ? (
                      <>
                        <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                        Connecting...
                      </>
                    ) : (
                      "Connect to Figma"
                    )}
                  </button>
                )}
              </div>
            )}

            {/* Figma File URL Section */}
            <div className="mb-6">
              <h3 className="text-sm font-medium text-gray-900 mb-3">Figma File URL</h3>
              <input
                type="url"
                value={figmaLink}
                onChange={(e) => {
                  setFigmaLink(e.target.value);
                  if (!designName && e.target.value) {
                    const urlParts = e.target.value.split('/');
                    const fileName = urlParts[urlParts.length - 1] || 'Figma Design';
                    setDesignName(fileName.replace(/[^a-zA-Z0-9\s]/g, '').trim() || 'Figma Design');
                  }
                }}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all placeholder-gray-400"
                placeholder="https://figma.com/file/..."
                disabled={isExtractingFrames || (accessLevel === "private" && !isConnected)}
              />
              <p className={`text-xs mt-2 ${
                accessLevel === "private" && !isConnected
                  ? "text-orange-600"
                  : "text-gray-500"
              }`}>
                {accessLevel === "private"
                  ? isConnected
                    ? "Paste any public or private Figma file URL"
                    : "Connect to Figma first to access private files"
                  : "Paste any public Figma file URL"
                }
              </p>
            </div>

            {/* Action Buttons */}
            <div className="flex gap-3">
              <button
                onClick={onClose}
                disabled={isExtractingFrames}
                className="flex-1 py-3 px-4 border border-gray-300 text-gray-700 rounded-lg font-medium hover:bg-gray-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Cancel
              </button>
              <button
                onClick={handleSubmit}
                disabled={isExtractingFrames || !figmaLink.trim() || (accessLevel === "private" && (!isConnected || isConnecting))}
                className={`flex-1 py-3 px-4 rounded-lg font-medium transition-all ${
                  isExtractingFrames || !figmaLink.trim() || (accessLevel === "private" && (!isConnected || isConnecting))
                    ? "bg-blue-300 cursor-not-allowed"
                    : "bg-blue-500 hover:bg-blue-600"
                } text-white`}
              >
                {isExtractingFrames ? "Importing..." : "Import"}
              </button>
            </div>
          </div>
        )}

        {/* Close button */}
        <button
          onClick={onClose}
          className="absolute top-4 right-4 text-white hover:text-gray-200 transition-colors z-10"
          disabled={isExtractingFrames}
        >
          <X size={20} />
        </button>
      </div>
    </div>
  );
};

export default FigmaImportModal;
