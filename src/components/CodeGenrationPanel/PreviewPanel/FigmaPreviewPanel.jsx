"use client";

import React, { useState, useEffect, use<PERSON><PERSON>back, useRef, useContext } from "react";
import {
  Loader2,
  <PERSON>freshCw,
  AlertTriangle,
  AlertCircle,
  Square,
  ChevronDown,
  ZoomIn,
  ZoomOut,
  RotateCcw,
  Monitor,
  Play,
  Code,
  CheckCircle,
  Eye,
  Figma,
  ChevronLeft,
  ChevronRight
} from "lucide-react";
import { Sandpack, SandpackPreview, SandpackProvider } from '@codesandbox/sandpack-react';
import Frame from 'react-frame-component'
import { useCodeGeneration } from "@/components/Context/CodeGenerationContext";
import { useFigmaScreen } from "@/components/Context/FigmaScreenContext";
import { ContainerStatusTypes } from "@/components/CodeGenrationPanel/PreviewPanel/ContainerTypes";
import { AlertContext } from "@/components/NotificationAlertService/AlertList";
import { useParams } from "next/navigation";
import Cookies from "js-cookie";
import { BootstrapTooltip } from "@/components/UIComponents/ToolTip/Tooltip-material-ui";

/**
 * Changes port from 3000 to 4000 in preview URLs
 * @param {string} url - The URL to process
 * @returns {string} - The URL with updated port
 */
const processPreviewUrl = (url) => {
  if (!url || typeof url !== 'string') return url;
  // Replace any occurrence of :3000 with :4000 in the URL
  return url.replace(':3000', ':4000');
};

/**
 * Converts screen name to HTML filename
 * @param {string} screenName - The screen name (e.g., "TODO PAGE")
 * @returns {string} - The HTML filename (e.g., "todo-page.html")
 */
const convertScreenNameToFileName = (screenName, screen_id) => {
  if (!screenName || typeof screenName !== 'string') return '';

  const baseName = screenName
    .toLowerCase()
    .replace(/\//g, '-') 
    .replace(/[^a-z0-9\s-]/g, '')
    .replace(/\s+/g, '-')
    .replace(/-+/g, '-')
    .replace(/^-|-$/g, '');

  const cleanId = String(screen_id)
    .replace(/[^a-z0-9]/gi, '-')
    .replace(/-+/g, '-')
    .replace(/^-|-$/g, '');

  return `${baseName}-${cleanId}.html`;
};

/**
 * Sends WebSocket message to fetch HTML content
 * @param {WebSocket} wsConnection - WebSocket connection
 * @param {string} fileName - HTML filename
 * @param {string} screenName - Screen name for JSON file lookup
 */
const fetchFigmaHTMLViaWebSocket = (wsConnection, fileName, screenName = null) => {
  if (wsConnection?.readyState === WebSocket.OPEN) {
    const message = {
      type: 'fetch_asset_screen',
      filename: fileName,
      screen_name: screenName
    };
    wsConnection.send(JSON.stringify(message));
    return true;
  } else {
    console.warn('WebSocket connection is not open, readyState:', wsConnection?.readyState);
    return false;
  }
};


// Helper function to manage persistent generation state
const getStoredGenerationState = (projectId) => {
  try {
    const stored = sessionStorage.getItem(`generatingScreens_${projectId}`);
    return stored ? new Set(JSON.parse(stored)) : new Set();
  } catch (error) {
    console.warn('Failed to parse stored generation state:', error);
    return new Set();
  }
};

const setStoredGenerationState = (projectId, generatingScreens) => {
  try {
    sessionStorage.setItem(`generatingScreens_${projectId}`, JSON.stringify([...generatingScreens]));
  } catch (error) {
    console.warn('Failed to store generation state:', error);
  }
};

const getCurrentProcessingScreen = (projectId) => {
  try {
    return sessionStorage.getItem(`currentProcessing_screen-${projectId}`);
  } catch (error) {
    console.warn('Failed to get current processing screen:', error);
    return null;
  }
};

const setCurrentProcessingScreen = (projectId, screenId) => {
  try {
    if (screenId) {
      sessionStorage.setItem(`currentProcessing_screen-${projectId}`, screenId);
    } else {
      sessionStorage.removeItem(`currentProcessing_screen-${projectId}`);
    }
  } catch (error) {
    console.warn('Failed to set current processing screen:', error);
  }
};

const getStoredLastClickedScreen = (projectId) => {
  try {
    return sessionStorage.getItem(`lastClickedScreen_${projectId}`);
  } catch (error) {
    console.warn('Failed to get stored last clicked screen:', error);
    return null;
  }
};

const setStoredLastClickedScreen = (projectId, screenId) => {
  try {
    if (screenId) {
      sessionStorage.setItem(`lastClickedScreen_${projectId}`, screenId);
    } else {
      sessionStorage.removeItem(`lastClickedScreen_${projectId}`);
    }
  } catch (error) {
    console.warn('Failed to store last clicked screen:', error);
  }
};

// Add a helper function to store selected screen in component state
const useLocalSelectedScreen = (projectId) => {
  const [localSelectedScreen, setLocalSelectedScreen] = useState(null);

  // Store in component state
  const setLocalScreen = useCallback((screen) => {
    setLocalSelectedScreen(screen);
  }, []);

  // Get from component state
  const getLocalScreen = useCallback(() => {
    return localSelectedScreen;
  }, [localSelectedScreen]);

  return { getLocalScreen, setLocalScreen };
};

const FigmaPreviewPanel = ({
  currentTaskId,
  customUrl = "",
  showMode = "split",
  figmaData = [],
  figmaLoader = false,
  externalSelectedScreen = null,
  onScreenSelect = null
}) => {
  const {
    wsConnection,
    containers,
    selectedContainer,
    setSelectedContainer,
    setUploadedAttachments,
    setInputValue,
    isAiTyping,
    activeReplyTo
  } = useCodeGeneration();

  const { projectId } = useParams();
  const tenantId = Cookies.get("tenant_id");

  // Get the figma screen context
  const { getSelectedScreen, setSelectedScreen: setContextSelectedScreen } = useFigmaScreen();

  // Add local state as fallback
  const { getLocalScreen, setLocalScreen } = useLocalSelectedScreen(projectId);

  // Add a stable reference for figmaData to prevent unnecessary re-renders
  const figmaDataRef = useRef(figmaData);
  useEffect(() => {
    figmaDataRef.current = figmaData;
  }, [figmaData]);

  // Add loading state to prevent flickering
  const [isInitializing, setIsInitializing] = useState(true);

  // Grace period to avoid showing "No Figma Designs" while data is about to arrive
  useEffect(() => {
    if (isInitializing && !figmaLoader && (!figmaData || figmaData.length === 0)) {
      const timeoutId = setTimeout(() => setIsInitializing(false), 3000);
      return () => clearTimeout(timeoutId);
    }
  }, [isInitializing, figmaLoader, figmaData]);

  // State for panel controls
  const [leftPanelFullscreen, setLeftPanelFullscreen] = useState(false);
  const [rightPanelFullscreen, setRightPanelFullscreen] = useState(false);

  // State to track hover position on divider
  const [hoverSide, setHoverSide] = useState(null); // 'left', 'right', or null

  // Initialize states with persistent data
  const [generatingScreens, setGeneratingScreens] = useState(() =>
    getStoredGenerationState(projectId)
  );
  const [lastClickedScreen, setLastClickedScreen] = useState(() =>
    getStoredLastClickedScreen(projectId)
  );
  const [currentProcessingScreenId, setCurrentProcessingScreenId] = useState(() =>
    getCurrentProcessingScreen(projectId)
  );

  const [iframeKey, setIframeKey] = useState(Date.now());
  const [iframeError, setIframeError] = useState(false);
  const [errorType, setErrorType] = useState(null);
  const [isCheckingUrl, setIsCheckingUrl] = useState(false);
  const [urlStatus, setUrlStatus] = useState('checking');
  const [customIframeKey, setCustomIframeKey] = useState(Date.now());

  // Figma-specific states
  // Use external selectedScreen if provided, otherwise use context or local state
  const [internalSelectedScreen, setInternalSelectedScreen] = useState(null);

  // Use external, context, or internal selected screen (in that order of priority)
  const selectedScreen = externalSelectedScreen || getSelectedScreen(projectId) || getLocalScreen() || internalSelectedScreen;

  // Function to update selected screen that updates context and notifies parent if callback exists
  const setSelectedScreen = useCallback((screen) => {
    setInternalSelectedScreen(screen);
    setLocalScreen(screen);
    setContextSelectedScreen(projectId, screen);
    if (onScreenSelect && typeof onScreenSelect === 'function') {
      onScreenSelect(screen);
    }
  }, [onScreenSelect, projectId, setContextSelectedScreen, setLocalScreen]);

  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [zoomLevel, setZoomLevel] = useState(1);
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const [imagePosition, setImagePosition] = useState({ x: 0, y: 0 });
  const [useAllScreens, setUseAllScreens] = useState(false);

  // Enhanced states for better UX
  const [htmlContent, setHtmlContent] = useState(null);
  const [isLoadingHTML, setIsLoadingHTML] = useState(false);
  const [htmlError, setHtmlError] = useState(null);
  const [showSandpack, setShowSandpack] = useState(false);
  const [pendingHtmlRequest, setPendingHtmlRequest] = useState(null);

  // Track processed screens in component state
  const [processedScreenIds, setProcessedScreenIds] = useState(new Set());

  // Removed local aiTyping state - we'll rely on isAiTyping from context and persistent storage

  const [htmlZoomLevel, setHtmlZoomLevel] = useState(1);
  const [isHtmlDragging, setIsHtmlDragging] = useState(false);
  const [htmlDragStart, setHtmlDragStart] = useState({ x: 0, y: 0 });
  const [htmlPosition, setHtmlPosition] = useState({ x: 0, y: 0 });
  const [processedScreenFiles, setProcessedScreenFiles] = useState(new Set());

  const { showAlert } = useContext(AlertContext);
  const [prevIsAiTyping, setPrevIsAiTyping] = useState(isAiTyping);
  const [lastFetchAttempt, setLastFetchAttempt] = useState(0);
  const fetchTimeoutRef = useRef(null);
  // Refs
  const iframeRef = useRef(null);
  const customIframeRef = useRef(null);
  const dropdownRef = useRef(null);
  const imageContainerRef = useRef(null);
  const htmlPreviewRef = useRef(null);
  const htmlContentContainerRef = useRef(null); // Add ref for HTML content container
  const wsMessageHandlerRef = useRef(null); // Add ref to track message handler
  const lastFetchTimeRef = useRef(0); // Add debounce ref to prevent rapid calls


  // Update persistent storage when state changes
  useEffect(() => {
    setStoredGenerationState(projectId, generatingScreens);
  }, [generatingScreens, projectId]);

  useEffect(() => {
    setStoredLastClickedScreen(projectId, lastClickedScreen);
  }, [lastClickedScreen, projectId]);

  // Helper function to update generating screens state
  const updateGeneratingScreens = useCallback((updateFn) => {
    setGeneratingScreens(prev => {
      const updated = updateFn(prev);
      setStoredGenerationState(projectId, updated);
      return updated;
    });
  }, [projectId]);

  // Helper function to update last clicked screen
  const updateLastClickedScreen = useCallback((screenId) => {
    setLastClickedScreen(screenId);
    setStoredLastClickedScreen(projectId, screenId);
  }, [projectId]);

  // Fetch HTML content for selected screen via WebSocket
  const fetchScreenHTML = useCallback(async (screen) => {
    if (!screen || !projectId || !tenantId) {
      return;
    }

    // Debounce: prevent rapid successive calls (minimum 1 second between calls)
    const now = Date.now();
    if (now - lastFetchTimeRef.current < 1000) {
      console.log('Debouncing fetchScreenHTML call - too soon since last fetch');
      return;
    }
    lastFetchTimeRef.current = now;

    const fileName = convertScreenNameToFileName(screen.screen_name, screen.screen_id);
    if (!fileName) {
      console.warn('Could not generate filename from screen name:', screen.screen_name || screen.name);
      return;
    }

    setIsLoadingHTML(true);
    setHtmlError(null);
    setShowSandpack(false);
    setPendingHtmlRequest(fileName);

    try {
      const success = fetchFigmaHTMLViaWebSocket(wsConnection, fileName, screen.screen_name);
      if (!success) {
        throw new Error('Failed to send WebSocket message - connection not ready');
      }
    } catch (error) {
      console.error('Error requesting HTML for screen:', screen.screen_name || screen.name, error);
      setHtmlError(error.message);
      setHtmlContent(null);
      setShowSandpack(false);
      setIsLoadingHTML(false);
      setPendingHtmlRequest(null);
    }
  }, [projectId, tenantId, wsConnection]);

  // Handle play icon click
  const handlePlayIconClick = useCallback(() => {
    if (!selectedScreen) {
      showAlert("We couldn't find any Figma screens to turn into code. Try adding or selecting a design to get started.", "error");
      return;
    }

    try {
      const screen_name = selectedScreen.screen_name || selectedScreen.name;
      const screen_id = selectedScreen.screen_id || selectedScreen.id;

      if (!screen_name || !screen_id) {
        return;
      }

      // Check if this project-screen is already processed and AI is typing
      // if (isAiTyping) {
      //   showAlert("The code generation is already in progress kindly perform operation once it is done.", "error");
      //   return;
      // }

      // Check if any screen is being generated
      if (generatingScreens.size > 0) {
        showAlert("Code generation is already in progress for another screen. Please wait until it completes.", "error");
        return;
      }

      // Mark this screen as generating
      // updateGeneratingScreens(prev => new Set([...prev, screen_id]));
      // updateLastClickedScreen(screen_id);
      setCurrentProcessingScreen(projectId, screen_id)

      // Generate figma screen code
      const generateMessage = {
        type: "generate_figma_screen_code",
        screen_name: screen_name,
        screen_id: screen_id
      };

      if (wsConnection?.readyState === WebSocket.OPEN) {
        wsConnection.send(JSON.stringify(generateMessage));
      }

      // Send message with attachments
      const extractedAttachments = [{
        attachment_id: screen_id,
        filename: `screen_${screen_id}.json`,
        file_location: selectedScreen.image_url || '',
        file_type: "figma_extracted_screen",
        size: null,
        screen_data: selectedScreen
      }];

      const sendMessageData = {
        type: "send_message",
        content: "Extract Figma JSON and convert to HTML/CSS/JS code",
        parent_id: activeReplyTo,
        attachments: extractedAttachments
      };

      setHtmlError(null);

      if (wsConnection?.readyState === WebSocket.OPEN) {
        wsConnection.send(JSON.stringify(sendMessageData));
      }

    } catch (error) {
      console.error('FigmaPreviewPanel: Error handling play icon click:', error);
      // Remove from generating screens on error
      const screen_id = selectedScreen.screen_id || selectedScreen.id;
      updateGeneratingScreens(prev => {
        const newSet = new Set(prev);
        newSet.delete(screen_id);
        return newSet;
      });
    }
  }, [selectedScreen, wsConnection, activeReplyTo, isAiTyping, projectId, showAlert]);

  // Handle review button click
  const handleReviewClick = useCallback(() => {
    if (!selectedScreen) {
      showAlert("We couldn't find any Figma screens to review. Try adding or selecting a design to get started.", "error");
      return;
    }

    try {
      const screen_name = selectedScreen.screen_name || selectedScreen.name;
      const screen_id = selectedScreen.screen_id || selectedScreen.id;

      if (!screen_name || !screen_id) {
        return;
      }

      // Check if this project-screen is already processed and AI is typing
      if (isAiTyping) {
        showAlert("The code generation is already in progress kindly perform operation once it is done.", "error");
        return;
      }

      // Mark this screen as generating
      updateGeneratingScreens(prev => new Set([...prev, screen_id]));
      updateLastClickedScreen(screen_id);

      // Generate figma screen code for review
      const generateMessage = {
        type: "generate_figma_screen_code",
        screen_name: screen_name,
        screen_id: screen_id
      };

      if (wsConnection?.readyState === WebSocket.OPEN) {
        wsConnection.send(JSON.stringify(generateMessage));
      }

      // Send message with attachments for review
      const extractedAttachments = [{
        attachment_id: screen_id,
        filename: `screen_${screen_id}.json`,
        file_location: selectedScreen.image_url || '',
        file_type: "figma_extracted_screen",
        size: null,
        screen_data: selectedScreen
      }];

      const sendMessageData = {
        type: "send_message",
        content: "Review the figma to html/css/js extraction and update based on issues found",
        parent_id: activeReplyTo,
        attachments: extractedAttachments
      };

      setHtmlError(null);

      if (wsConnection?.readyState === WebSocket.OPEN) {
        wsConnection.send(JSON.stringify(sendMessageData));
      }

    } catch (error) {
      console.error('FigmaPreviewPanel: Error handling review button click:', error);
      // Remove from generating screens on error
      const screen_id = selectedScreen.screen_id || selectedScreen.id;
      updateGeneratingScreens(prev => {
        const newSet = new Set(prev);
        newSet.delete(screen_id);
        return newSet;
      });
    }
  }, [selectedScreen, wsConnection, activeReplyTo, isAiTyping, projectId, showAlert, updateGeneratingScreens, updateLastClickedScreen]);

  // WebSocket message handler
  const handleWebSocketMessage = useCallback((event) => {
    try {
      const data = JSON.parse(event.data);
      // console.error("data", data)
      if (data.type === "stop_streaming" && data.data.status === "success") {
        setIsLoadingHTML(false);
        const fileName = convertScreenNameToFileName(selectedScreen?.screen_name || selectedScreen?.name, selectedScreen?.screen_id)
        const currentProcessingScreenId = getCurrentProcessingScreen(projectId)
        if ((selectedScreen?.screen_id || selectedScreen?.id) === currentProcessingScreenId) {
          setCurrentProcessingScreen(projectId)
        }
      }

      if (data.type === 'fetch_asset_screen_response') {
        // if (!pendingHtmlRequest) {
        //   return;
        // }

        const responseData = data.data || {};
        // console.error("responseData", responseData)
        setIsLoadingHTML(false);
        setPendingHtmlRequest(null);

        if (responseData.html_content) {
          // console.error("inside if html content", responseData.html_content)
          setIsLoadingHTML(false);
          setHtmlContent(responseData.html_content);
          setShowSandpack(true);
          setHtmlError(null);
          const fileName = convertScreenNameToFileName(selectedScreen?.screen_name || selectedScreen?.name, selectedScreen?.screen_id)
          const currentProcessingScreenId = getCurrentProcessingScreen(projectId)
          if (fileName === responseData?.filename && (selectedScreen?.screen_id || selectedScreen?.id) === currentProcessingScreenId) {
            setCurrentProcessingScreen(projectId)
          }

          // Clear generation state for the current selected screen when HTML content is received
          // if (selectedScreen) {
          //   const currentScreenId = selectedScreen.screen_id || selectedScreen.id;

          //   // Mark this screen as processed by adding it to our processedScreenIds Set
          //   setProcessedScreenIds(prev => {
          //     const newSet = new Set(prev);
          //     newSet.add(currentScreenId);
          //     return newSet;
          //   });

          //   updateGeneratingScreens(prev => {
          //     const newSet = new Set(prev);
          //     newSet.delete(currentScreenId);
          //     return newSet;
          //   });

          //   // Clear last clicked screen if it matches the current screen
          //   if (lastClickedScreen === currentScreenId) {
          //     updateLastClickedScreen(null);
          //   }
          // }
          return;
        }

        // Case 2: No HTML file available - show play icon
        if (responseData.status === 'no_html' && responseData.show_play_icon) {
          setIsLoadingHTML(false);
          // console.error('No HTML file available, showing play icon');
          setHtmlContent(null);
          setShowSandpack(false);
          setHtmlError(null); // Clear error to show play icon
          // const fileName = convertScreenNameToFileName(selectedScreen?.screen_name || selectedScreen?.name)
          // const currentProcessingScreenId = getCurrentProcessingScreen(projectId)
          // if (fileName === responseData?.filename && (selectedScreen?.screen_id || selectedScreen?.id) === currentProcessingScreenId) {
          //   setCurrentProcessingScreen(projectId)
          // }

          // Remove from processed screens if it was previously marked as processed
          // if (selectedScreen) {
          //   const currentScreenId = selectedScreen.screen_id || selectedScreen.id;
          //   setProcessedScreenIds(prev => {
          //     const newSet = new Set(prev);
          //     newSet.delete(currentScreenId);
          //     return newSet;
          //   });
          // }

          // Store screen info for play icon functionality
          setSelectedScreen({
            ...selectedScreen,
            screen_name: responseData.screen_name,
            needs_generation: true
          });
          return;
        }

        // Case 3: Error - check if it's "HTML doesn't exist" vs real error
        if (responseData.error) {
          // Check if error indicates HTML file doesn't exist (should show play icon)
          const errorMessage = responseData.error.toLowerCase();
          const isHtmlMissingError = errorMessage.includes('could not retrieve the html file') ||
            errorMessage.includes('html file not found') ||
            errorMessage.includes('missing from the path') ||
            errorMessage.includes('may be missing');

          if (isHtmlMissingError) {
            console.log('HTML file missing, showing play icon instead of error');
            setHtmlContent(null);
            setShowSandpack(false);
            setIsLoadingHTML(false);
            setHtmlError(null); // Clear error to show play icon

            // Remove from processed screens if it was previously marked as processed
            // if (selectedScreen) {
            //   const currentScreenId = selectedScreen.screen_id || selectedScreen.id;
            //   setProcessedScreenIds(prev => {
            //     const newSet = new Set(prev);
            //     newSet.delete(currentScreenId);
            //     return newSet;
            //   });
            // }

            // Mark screen as needing generation
            setSelectedScreen({
              ...selectedScreen,
              needs_generation: true
            });
            return;
          }

          // Real error - show error message
          // console.error('Real error response received:', responseData.error);
          setHtmlContent(null);
          setShowSandpack(false);
          setHtmlError(responseData.error);
          return;
        }

        // Fallback: If unexpected response format, show play icon
        console.log('Unexpected response format, showing play icon');
        setHtmlContent(null);
        setShowSandpack(false);
        setHtmlError(null);
      }

      // Handle generation completion
      if (data.type === 'success' && data.data?.message?.includes('Started code generation')) {
        console.log('Code generation started successfully:', data.data);
      }

      if (data.type === 'error' && data.data?.error?.includes('screen')) {
        // console.error('Code generation error:', data.data.error);
        setIsLoadingHTML(false);
        setHtmlError(data.data.error);

        // Remove from generating screens on error
        // updateGeneratingScreens(prev => {
        //   const newSet = new Set(prev);
        //   if (lastClickedScreen) {
        //     newSet.delete(lastClickedScreen);
        //   }
        //   return newSet;
        // });
      }

      // Handle HTML generation completion
      if (data.type === 'figma_html_generated') {
        // console.error('HTML generation completed:', data.data);

        // Get the screen ID from the data if available
        // const generatedScreenId = data.data?.screen_id;

        // // If we have a screen ID, mark it as processed
        // if (generatedScreenId) {
        //   setProcessedScreenIds(prev => {
        //     const newSet = new Set(prev);
        //     newSet.add(generatedScreenId);
        //     return newSet;
        //   });
        // }

        // // Remove from generating screens
        // updateGeneratingScreens(prev => {
        //   const newSet = new Set(prev);
        //   if (lastClickedScreen) {
        //     newSet.delete(lastClickedScreen);
        //   }
        //   return newSet;
        // });

        // if (selectedScreen) {
        //   setTimeout(() => {
        //     fetchScreenHTML(selectedScreen);
        //   }, 1000);
        // }
      }

      // Clear generating state when AI stops typing
      if (data.type === 'ai_typing_stopped' || data.type === 'message_complete') {
        setCurrentProcessingScreen(projectId)
      }

    } catch (error) {
      // console.error('Error parsing WebSocket message:', error);
    }
  }, [selectedScreen, fetchScreenHTML, currentProcessingScreenId]);

  // WebSocket message listener setup
  useEffect(() => {
    if (!wsConnection) {
      return;
    }

    if (wsMessageHandlerRef.current) {
      wsConnection.removeEventListener('message', wsMessageHandlerRef.current);
    }

    wsMessageHandlerRef.current = handleWebSocketMessage;
    wsConnection.addEventListener('message', handleWebSocketMessage);

    return () => {
      if (wsConnection && wsMessageHandlerRef.current) {
        wsConnection.removeEventListener('message', wsMessageHandlerRef.current);
        wsMessageHandlerRef.current = null;
      }
    };
  }, [wsConnection, handleWebSocketMessage]);

  // Initialize selected screen only once when figmaData is first loaded
  useEffect(() => {
    if (isInitializing && figmaData?.length > 0) {
      if (!selectedScreen) {
        setSelectedScreen(figmaData[0]);
      } else {
        // If we already have a selected screen, check if it still exists in the new data
        const existingScreenId = selectedScreen.screen_id || selectedScreen.id;
        const existingScreen = figmaData.find(
          screen => (screen.screen_id || screen.id) === existingScreenId
        );

        if (!existingScreen) {
          setSelectedScreen(figmaData[0]);
        } else if (existingScreen !== selectedScreen) {
          // Update with the fresh reference if it's different
          setSelectedScreen(existingScreen);
        }
      }
      setIsInitializing(false);
    }
  }, [figmaData, selectedScreen, setSelectedScreen, isInitializing]);

  // Auto-select the first screen if data arrives after initialization and nothing is selected
  useEffect(() => {
    if (!isInitializing && !selectedScreen && figmaData?.length > 0) {
      setSelectedScreen(figmaData[0]);
    }
  }, [isInitializing, figmaData, selectedScreen, setSelectedScreen]);

  // Replace the existing useEffect for figmaData changes with a more stable version
  useEffect(() => {
    if (!isInitializing && figmaData?.length > 0) {
      // Only update if the selected screen is no longer in the figmaData
      if (selectedScreen) {
        const existingScreenId = selectedScreen.screen_id || selectedScreen.id;
        const stillExists = figmaData.some(
          screen => (screen.screen_id || screen.id) === existingScreenId
        );

        if (!stillExists) {
          // Only update if the screen is no longer available
          setSelectedScreen(figmaData[0]);
        }
      }
    } else if (figmaData?.length === 0 && selectedScreen) {
      setSelectedScreen(null);
    }
  }, [figmaData, selectedScreen, setSelectedScreen, isInitializing]);

  // Fetch HTML when selectedScreen changes, but with debouncing
  const fetchHtmlTimeoutRef = useRef(null);

  useEffect(() => {
    if (selectedScreen) {
      const screen_id = selectedScreen.screen_id || selectedScreen.id;
      fetchScreenHTML(selectedScreen);

    }
  }, [selectedScreen, fetchScreenHTML,]);
  useEffect(() => {
    if (selectedScreen && !isAiTyping && !isLoadingHTML) {
      // Clear any pending timeout
      if (fetchTimeoutRef.current) {
        clearTimeout(fetchTimeoutRef.current);
      }

      // Fetch with a small delay to prevent rapid successive calls
      fetchTimeoutRef.current = setTimeout(() => {
        fetchScreenHTML(selectedScreen);
      }, 200);
    }
  }, [selectedScreen, isAiTyping, isLoadingHTML, fetchScreenHTML]);

  useEffect(() => {
    // Track when isAiTyping changes from true to false
    if (prevIsAiTyping && !isAiTyping && selectedScreen) {
      // console.error('AI typing stopped, immediately fetching HTML for selected screen');

      // Clear any pending fetch timeout
      if (fetchTimeoutRef.current) {
        clearTimeout(fetchTimeoutRef.current);
      }

      // Immediate fetch when AI stops typing
      const now = Date.now();
      const timeSinceLastFetch = now - lastFetchAttempt;

      if (timeSinceLastFetch > 500) { // Minimum 500ms between fetches
        setLastFetchAttempt(now);
        fetchScreenHTML(selectedScreen);
      } else {
        // Schedule fetch after minimum interval
        fetchTimeoutRef.current = setTimeout(() => {
          setLastFetchAttempt(Date.now());
          fetchScreenHTML(selectedScreen);
        }, 500 - timeSinceLastFetch);
      }
    }

    // Update previous state
    setPrevIsAiTyping(isAiTyping);
  }, [isAiTyping, prevIsAiTyping, selectedScreen, fetchScreenHTML, lastFetchAttempt]);
  useEffect(() => {
    return () => {
      if (fetchTimeoutRef.current) {
        clearTimeout(fetchTimeoutRef.current);
      }
    };
  }, []);


  // Watch isAiTyping to refetch HTML when AI stops typing
  // useEffect(() => {
  //   if (!isAiTyping && selectedScreen) {
  //     const screen_id = selectedScreen.screen_id || selectedScreen.id;

  //     // Only refetch if this screen was being generated
  //     if (generatingScreens.has(screen_id)) {
  //       setTimeout(() => {
  //         fetchScreenHTML(selectedScreen);
  //       }, 1000);
  //     }
  //     else if (processedScreenIds.has(screen_id) && !htmlContent && !isLoadingHTML) {
  //       setTimeout(() => {
  //         fetchScreenHTML(selectedScreen);
  //       }, 500);
  //     }
  //   }
  // }, [isAiTyping, selectedScreen, fetchScreenHTML, generatingScreens, processedScreenIds, htmlContent, isLoadingHTML]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Auto-select first container if none is selected
  useEffect(() => {
    if (!selectedContainer && containers.length > 0) {
      setSelectedContainer(containers[0].name);
    }
  }, [selectedContainer, containers, setSelectedContainer]);

  // Find the selected container object
  const getSelectedContainerObj = useCallback(() => {
    return containers.find(c => c.name === selectedContainer);
  }, [containers, selectedContainer]);


  // Restart container method
  const restartContainer = useCallback((containerName = selectedContainer) => {
    if (wsConnection?.readyState === WebSocket.OPEN && currentTaskId) {
      wsConnection.send(JSON.stringify({
        type: "restart_container",
        task_id: currentTaskId,
        input_data: {
          container_name: containerName
        }
      }));
    }
  }, [wsConnection, currentTaskId, selectedContainer]);

  // Start container method (alias for restart)
  const startContainer = useCallback((containerName = selectedContainer) => {
    restartContainer(containerName);
  }, [restartContainer, selectedContainer]);


  // Manual refresh function for main iframe
  const refreshIframe = useCallback(() => {
    setIframeError(false);
    setErrorType(null);
    setIsCheckingUrl(false);
    setUrlStatus('checking');
    setIframeKey(Date.now());
  }, []);

  // Manual refresh function for custom iframe
  const refreshCustomIframe = useCallback(() => {
    setCustomIframeKey(Date.now());
  }, []);

  // Zoom functions for Figma panel
  const zoomIn = useCallback(() => {
    setZoomLevel(prev => {
      const newZoom = Math.min(prev + 0.1, 3);
      // Keep current position when zooming
      return newZoom;
    });
  }, []);

  const zoomOut = useCallback(() => {
    setZoomLevel(prev => {
      const newZoom = Math.max(prev - 0.1, 0.25);
      // Keep current position when zooming
      return newZoom;
    });
  }, []);

  const resetZoom = useCallback(() => {
    setZoomLevel(1);
    setImagePosition({ x: 0, y: 0 });
  }, []);

  // Drag functions
  const handleMouseDown = useCallback((e) => {
    if (zoomLevel > 1) {
      setIsDragging(true);
      setDragStart({
        x: e.clientX - imagePosition.x,
        y: e.clientY - imagePosition.y
      });
    }
  }, [zoomLevel, imagePosition]);

  const handleMouseMove = useCallback((e) => {
    if (isDragging) {
      setImagePosition({
        x: e.clientX - dragStart.x,
        y: e.clientY - dragStart.y
      });
    }
  }, [isDragging, dragStart]);

  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
  }, []);

  // Update the zoom functions to work with content-based zooming
  const htmlZoomIn = useCallback(() => {
    setHtmlZoomLevel(prev => {
      const newZoom = Math.min(prev + 0.1, 3);
      // Zoom in from center of the viewport
      if (newZoom > prev) {
        const zoomRatio = newZoom / prev;
        setHtmlPosition(current => ({
          x: current.x * zoomRatio,
          y: current.y * zoomRatio
        }));
      }
      return newZoom;
    });
  }, []);

  const htmlZoomOut = useCallback(() => {
    setHtmlZoomLevel(prev => {
      const newZoom = Math.max(prev - 0.1, 0.25);
      // Zoom out from center of the viewport
      if (newZoom < prev) {
        const zoomRatio = newZoom / prev;
        setHtmlPosition(current => ({
          x: current.x * zoomRatio,
          y: current.y * zoomRatio
        }));
      }
      return newZoom;
    });
  }, []);

  const resetHtmlZoom = useCallback(() => {
    setHtmlZoomLevel(1);
    setHtmlPosition({ x: 0, y: 0 });
  }, []);

  // Enhanced pan functionality that works at any zoom level
  const handleHtmlMouseDown = useCallback((e) => {
    // Enable dragging at any zoom level, not just when zoomed in
    setIsHtmlDragging(true);
    setHtmlDragStart({
      x: e.clientX - htmlPosition.x,
      y: e.clientY - htmlPosition.y
    });
    e.preventDefault();
  }, [htmlPosition]);

  const handleHtmlMouseMove = useCallback((e) => {
    if (isHtmlDragging) {
      const newX = e.clientX - htmlDragStart.x;
      const newY = e.clientY - htmlDragStart.y;

      setHtmlPosition({ x: newX, y: newY });
    }
  }, [isHtmlDragging, htmlDragStart]);

  const handleHtmlMouseUp = useCallback(() => {
    setIsHtmlDragging(false);
  }, [])

  // Enhanced pan functionality for Figma image panel
  const handleFigmaMouseDown = useCallback((e) => {
    // Enable dragging at any zoom level for Figma panel
    setIsDragging(true);
    setDragStart({
      x: e.clientX - imagePosition.x,
      y: e.clientY - imagePosition.y
    });
    e.preventDefault();
  }, [imagePosition]);

  const handleFigmaMouseMove = useCallback((e) => {
    if (isDragging) {
      const newX = e.clientX - dragStart.x;
      const newY = e.clientY - dragStart.y;

      setImagePosition({ x: newX, y: newY });
    }
  }, [isDragging, dragStart]);

  const handleFigmaMouseUp = useCallback(() => {
    setIsDragging(false);
  }, []);

  // Double click to reset zoom and position for Figma panel
  const handleFigmaDoubleClick = useCallback(() => {
    resetZoom();
  }, [resetZoom]);

  // Double click to reset zoom and position
  const handleHtmlDoubleClick = useCallback(() => {
    resetHtmlZoom();
  }, [resetHtmlZoom]);

  // Add handleScreenSelection function
  const handleScreenSelection = useCallback((screen) => {
    setIsLoadingHTML(true);
    setSelectedScreen(screen);
    fetchScreenHTML(screen)
    setIsDropdownOpen(false);
    setUseAllScreens(false);
    resetZoom();
    setTimeout(() => {
    setIsLoadingHTML(false);
  }, 1000);

    // Clear any HTML content and show appropriate state for the new screen
    setHtmlContent(null);
    setShowSandpack(false);
    setHtmlError(null);
  }, [resetZoom, setSelectedScreen]);

  // Update custom iframe when URL changes
  useEffect(() => {
    if (customUrl) {
      setCustomIframeKey(Date.now());
    }
  }, [customUrl]);

  // Reset states when container changes
  useEffect(() => {
    setIframeError(false);
    setErrorType(null);
    setIsCheckingUrl(false);
    setUrlStatus('checking');
    setIframeKey(Date.now());
  }, [selectedContainer]);


  // Add a useEffect to handle screen selection to check if we need to fetch HTML
  // useEffect(() => {
  //   if (selectedScreen && !isAiTyping) {
  //     const currentScreenId = selectedScreen.screen_id || selectedScreen.id;

  //     // If this screen is processed but we don't have HTML content, fetch it
  //     if (processedScreenIds.has(currentScreenId) && !htmlContent && !isLoadingHTML) {
  //       fetchScreenHTML(selectedScreen);
  //     }
  //   }
  // }, [selectedScreen, processedScreenIds, htmlContent, isLoadingHTML, isAiTyping, fetchScreenHTML]);

  // // Replace isCurrentScreenBeingProcessed with a function that uses our component state
  // const isCurrentScreenBeingProcessed = useCallback(() => {
  //   if (!selectedScreen) return false;

  //   const currentScreenId = selectedScreen.screen_id || selectedScreen.id;
  //   return processedScreenIds.has(currentScreenId);
  // }, [selectedScreen, processedScreenIds]);

  // Prepare Sandpack files from HTML content
  const prepareSandpackFiles = useCallback(() => {
    if (!htmlContent) return {};

    const files = {
      '/index.html': {
        code: htmlContent || '<div>No HTML content available</div>'
      }
    };

    return files;
  }, [htmlContent]);

  // Debug function to log HTML content structure
  useEffect(() => {
    if (htmlContent) {
      console.log('HTML Content received:', {
        length: htmlContent.length,
        hasDoctype: htmlContent.includes('<!DOCTYPE'),
        hasHtml: htmlContent.includes('<html'),
        hasHead: htmlContent.includes('<head'),
        hasBody: htmlContent.includes('<body'),
        hasStyle: htmlContent.includes('<style'),
        preview: htmlContent.substring(0, 200) + '...'
      });
    }
  }, [htmlContent]);

  // Convert filename back to screen name for comparison
  // const convertFileNameToScreenName = (filename) => {
  //   // "todo-page.html" → "TODO PAGE"
  //   const nameWithoutExtension = filename.replace(/\.html$/i, '');
  //   return nameWithoutExtension.replace(/-/g, ' ').toUpperCase();
  // };

  // // Check if screen has been processed
  // const isScreenProcessed = (screen, processedFiles) => {
  //   const screenName = (screen.screen_name || screen.name || '').toUpperCase();

  //   for (const filename of processedFiles) {
  //     const convertedScreenName = convertFileNameToScreenName(filename);
  //     if (convertedScreenName === screenName) {
  //       return true;
  //     }
  //   }
  //   return false;
  // };

  const container = getSelectedContainerObj();

  // Enhanced Figma panel with dropdown and zoom
  const renderFigmaOnlyPanel = () => {
    // Show a loading state during initialization
    if (isInitializing && figmaData?.length > 0) {
      return (
        <div className="w-full h-full flex items-center justify-center flex-col gap-4 bg-gray-50 relative">
          <div className="w-10 h-10 relative">
            <div className="absolute inset-0 rounded-full border-2 border-gray-200"></div>
            <div className="absolute inset-0 rounded-full border-t-2 border-r-2 border-primary animate-spin"></div>
          </div>
          <p className="text-gray-600 text-sm font-medium">Initializing preview...</p>
        </div>
      );
    }

    // Check if figmaData exists and is not loading
    if (!figmaLoader && !isInitializing && (!figmaData || figmaData.length === 0)) {
      return (
        <div className="w-full h-full flex items-center justify-center flex-col gap-4 bg-gray-50 relative">
          <div className="p-4 rounded-full bg-white shadow-md">
            <Figma className="h-16 w-16 text-[#ff9800] animate-pulse" />
          </div>
          <div className="text-center">
            <p className="text-gray-600 text-lg font-medium mb-2">No Figma Designs</p>
            <p className="text-gray-500 text-sm max-w-md">
              No Figma designs have been added to this project yet. Add some designs to see them here.
            </p>
          </div>
        </div>
      );
    }

    // Check if selectedScreen is null to prevent TypeError
    if (!selectedScreen) {
      return (
        <div className="w-full h-full flex items-center justify-center flex-col gap-4 bg-gray-50 relative">
          <div className="p-4 rounded-full bg-white shadow-md">
            <Figma className="h-16 w-16 text-[#ff9800] animate-pulse" />
          </div>
          <div className="text-center">
            <p className="text-gray-600 text-lg font-medium mb-2">No Screen Selected</p>
            <p className="text-gray-500 text-sm max-w-md">
              No Figma screen is currently selected.
            </p>
          </div>
        </div>
      );
    }

    // Show loader in left panel when HTML is loading in right panel
    if (isLoadingHTML) {
      return (
        <div className="w-full h-full flex flex-col">
          {/* Header with dropdown and controls */}
          <div className="flex items-center justify-between p-2 sm:p-4 bg-gray-50 border-b border-gray-200">
            <div className="flex flex-col items-start gap-2" ref={dropdownRef}>
              <div className="relative w-full">
                <button
                  onClick={() => setIsDropdownOpen(!isDropdownOpen)}
                  className="flex items-center gap-2 px-2 sm:px-4 py-1 sm:py-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors min-w-[120px] sm:min-w-[200px]"
                >
                  <Monitor className="h-3 w-3 sm:h-4 sm:w-4 text-gray-500" />
                  <span className="text-xs sm:text-sm font-medium text-gray-700 truncate">
                    {selectedScreen.screen_name || selectedScreen.name}
                  </span>
                  <ChevronDown className={`h-3 w-3 sm:h-4 sm:w-4 text-gray-500 transition-transform ${isDropdownOpen ? 'rotate-180' : ''}`} />
                </button>

                {isDropdownOpen && (
                  <div className="absolute top-full left-0 mt-1 w-full bg-white border border-gray-200 rounded-lg shadow-lg z-10 max-h-60 overflow-y-auto">
                    {figmaData.map((screen) => (
                      <button
                        key={screen.screen_id || screen.id}
                        onClick={() => handleScreenSelection(screen)}
                        className={`w-full px-3 sm:px-4 py-2 sm:py-3 text-left hover:bg-gray-50 transition-colors border-b border-gray-100 last:border-b-0 ${(selectedScreen?.screen_id || selectedScreen?.id) === (screen.screen_id || screen.id)
                          ? 'bg-blue-50 text-blue-600'
                          : 'text-gray-700'
                          }`}
                      >
                        <div className="flex items-center justify-between w-full">
                          <div className="flex items-center gap-2">
                            <Monitor className="h-3 w-3 sm:h-4 sm:w-4" />
                            <span className="text-xs sm:text-sm font-medium">
                              {screen.screen_name || screen.name}
                            </span>
                          </div>
                          {getCurrentProcessingScreen(projectId) === screen.screen_id && (
                            <div className="w-3 h-3 rounded-full bg-green-500 animate-pulse"></div>
                          )}
                        </div>
                      </button>
                    ))}
                  </div>
                )}
              </div>
            </div>

            {/* Enhanced fullscreen button with better visibility */}
            {/* <div className="flex items-center gap-1 sm:gap-2">
              <button
                onClick={() => setLeftPanelFullscreen(!leftPanelFullscreen)}
                className="p-1.5 sm:p-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors shadow-md"
                title={leftPanelFullscreen ? "Exit Fullscreen" : "Fullscreen"}
              >
                {leftPanelFullscreen ? 
                  <ChevronLeft className="h-3 w-3 sm:h-4 sm:w-4 text-gray-600" /> : 
                  <ChevronRight className="h-3 w-3 sm:h-4 sm:w-4 text-gray-600" />
                }
              </button>
            </div> */}
          </div>

          {/* Show loading state in sync with right panel */}
          <div className="flex-1 flex items-center justify-center flex-col gap-4 bg-gray-100">
            <div className="w-10 h-10 relative">
              <div className="absolute inset-0 rounded-full border-2 border-gray-200"></div>
              <div className="absolute inset-0 rounded-full border-t-2 border-r-2 border-primary animate-spin"></div>
            </div>
            <p className="text-gray-600 text-sm font-medium">Loading preview...</p>
            <p className="text-gray-500 text-xs text-center max-w-md">
              Preparing design preview...
            </p>
          </div>
        </div>
      );
    }

    return (
      <div className="w-full h-full flex flex-col">
        {/* Header with dropdown and controls */}
        <div className="flex items-center justify-between p-2 sm:p-4 bg-gray-50 border-b border-gray-200">
          <div className="flex flex-col items-start gap-2" ref={dropdownRef}>
            <div className="relative w-full">
              <button
                onClick={() => setIsDropdownOpen(!isDropdownOpen)}
                className="flex items-center gap-2 px-2 sm:px-4 py-1 sm:py-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors min-w-[120px] sm:min-w-[200px]"
              >
                <Monitor className="h-3 w-3 sm:h-4 sm:w-4 text-gray-500" />
                <span className="text-xs sm:text-sm font-medium text-gray-700 truncate">
                  {selectedScreen.screen_name || selectedScreen.name}
                </span>
                <ChevronDown className={`h-3 w-3 sm:h-4 sm:w-4 text-gray-500 transition-transform ${isDropdownOpen ? 'rotate-180' : ''}`} />
              </button>

              {isDropdownOpen && (
                <div className="absolute top-full left-0 mt-1 w-full bg-white border border-gray-200 rounded-lg shadow-lg z-10 max-h-60 overflow-y-auto">
                  {figmaData.map((screen) => (
                    <button
                      key={screen.screen_id || screen.id}
                      onClick={() => handleScreenSelection(screen)}
                      className={`w-full px-3 sm:px-4 py-2 sm:py-3 text-left hover:bg-gray-50 transition-colors border-b border-gray-100 last:border-b-0 ${(selectedScreen?.screen_id || selectedScreen?.id) === (screen.screen_id || screen.id)
                        ? 'bg-blue-50 text-blue-600'
                        : 'text-gray-700'
                        }`}
                    >
                      <div className="flex items-center justify-between w-full">
                        <div className="flex items-center gap-2">
                          <Monitor className="h-3 w-3 sm:h-4 sm:w-4" />
                          <span className="text-xs sm:text-sm font-medium">
                            {screen.screen_name || screen.name}
                          </span>
                        </div>
                        {getCurrentProcessingScreen(projectId) === screen.screen_id && (
                          <div className="w-2 h-2 rounded-full bg-green-500 animate-pulse"></div>
                        )}
                      </div>
                    </button>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Zoom Controls and Fullscreen Button */}
          <div className="flex items-center gap-1 sm:gap-2">
            <button
              onClick={zoomOut}
              disabled={zoomLevel <= 0.25}
              className="p-1.5 sm:p-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              title="Zoom Out"
            >
              <ZoomOut className="h-3 w-3 sm:h-4 sm:w-4 text-gray-600" />
            </button>

            <div className="px-2 sm:px-3 py-1 bg-white border border-gray-300 rounded-lg min-w-[40px] sm:min-w-[60px] text-center">
              <span className="text-xs sm:text-sm font-medium text-gray-700">
                {Math.round(zoomLevel * 100)}%
              </span>
            </div>

            <button
              onClick={zoomIn}
              disabled={zoomLevel >= 3}
              className="p-1.5 sm:p-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              title="Zoom In"
            >
              <ZoomIn className="h-3 w-3 sm:h-4 sm:w-4 text-gray-600" />
            </button>

            <button
              onClick={resetZoom}
              className="p-1.5 sm:p-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
              title="Reset Zoom"
            >
              <RotateCcw className="h-3 w-3 sm:h-4 sm:w-4 text-gray-600" />
            </button>

            {/* Enhanced fullscreen button with better visibility */}
            {/* <button
              onClick={() => setLeftPanelFullscreen(!leftPanelFullscreen)}
              className="p-1.5 sm:p-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors shadow-md"
              title={leftPanelFullscreen ? "Exit Fullscreen" : "Fullscreen"}
            >
              {leftPanelFullscreen ? 
                <ChevronLeft className="h-3 w-3 sm:h-4 sm:w-4 text-gray-600" /> : 
                <ChevronRight className="h-3 w-3 sm:h-4 sm:w-4 text-gray-600" />
              }
            </button> */}
          </div>
        </div>

        {/* Image Display Area */}
        <div
          ref={imageContainerRef}
          className="flex-1 overflow-hidden bg-gray-100 relative"
          onMouseDown={handleFigmaMouseDown}
          onMouseMove={handleFigmaMouseMove}
          onMouseUp={handleFigmaMouseUp}
          onMouseLeave={handleFigmaMouseUp}
          onDoubleClick={handleFigmaDoubleClick}
          style={{
            cursor: isDragging ? 'grabbing' : 'grab'
          }}
        >
          <div
            className="w-full h-full flex items-center justify-center"
            style={{
              transform: `scale(${zoomLevel}) translate(${imagePosition.x / zoomLevel}px, ${imagePosition.y / zoomLevel}px)`,
              transformOrigin: 'center center',
              transition: isDragging ? 'none' : 'transform 0.1s ease-out'
            }}
          >
            <img
              src={selectedScreen.image_url}
              alt={selectedScreen.name}
              className="max-w-full max-h-full object-contain shadow-lg rounded-lg"
              style={{
                userSelect: 'none',
                pointerEvents: 'none'
              }}
              onError={(e) => {
                e.target.src = "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDMwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xMjUgNzVIMTc1VjEyNUgxMjVWNzVaIiBmaWxsPSIjOUI5RkFBIi8+CjxwYXRoIGQ9Ik0xMzUgOTVIMTY1VjEwNUgxMzVWOTVaIiBmaWxsPSIjNjM2OTc1Ii8+PC9zdmc+";
              }}
            />
          </div>
        </div>
      </div>
    );
  };

  // Update renderMainPanelContent to handle initialization state
  const renderMainPanelContent = () => {
    // Show loading state during initialization
    if (isInitializing && figmaData?.length > 0) {
      return (
        <div className="w-full h-full flex items-center justify-center flex-col gap-4">
          <div className="w-10 h-10 relative">
            <div className="absolute inset-0 rounded-full border-2 border-gray-200"></div>
            <div className="absolute inset-0 rounded-full border-t-2 border-r-2 border-primary animate-spin"></div>
          </div>
          <p className="text-gray-600 text-sm font-medium">Initializing preview...</p>
        </div>
      );
    }

    const currentScreenId = selectedScreen?.screen_id || selectedScreen?.id;

    // Check if the current selected screen is being generated
    // This condition now properly checks persistent state
    const isCurrentScreenGenerating = getCurrentProcessingScreen(projectId) === currentScreenId;

    // Show processing message ONLY when the currently selected screen is being generated
    if (getCurrentProcessingScreen(projectId) === currentScreenId) {
      return (
        <div className="w-full h-full flex items-center justify-center flex-col gap-4">
          <div className="w-10 h-10 relative">
            <div className="absolute inset-0 rounded-full border-2 border-gray-200"></div>
            <div className="absolute inset-0 rounded-full border-t-2 border-r-2 border-primary animate-spin"></div>
          </div>
          <p className="text-gray-600 text-sm font-medium">Processing your request...</p>
          <p className="text-gray-500 text-xs text-center max-w-md">
            Please wait while we generate the code for your Figma design.
          </p>
        </div>
      );
    }

    // Show loading message while fetching HTML
    if (isLoadingHTML) {
      return (
        <div className="w-full h-full flex items-center justify-center flex-col gap-4">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <p className="text-gray-600 text-sm font-medium">Loading preview...</p>
          <p className="text-gray-500 text-xs text-center max-w-md">
            Fetching HTML preview via WebSocket...
          </p>
        </div>
      );
    }

    // Show Sandpack if HTML content is available
    if (showSandpack && htmlContent) {
      const files = prepareSandpackFiles();

      return (
        <div className="w-full h-full flex flex-col">
          <div className="p-2 sm:p-4 bg-gray-50 border-b border-gray-200 flex items-center justify-between flex-shrink-0">
            <div className="flex items-center gap-2">
              <Code className="h-4 w-4 text-gray-600" />
              <span className="text-sm font-medium text-gray-700">
                Preview
              </span>
              {htmlZoomLevel > 1 && (
                <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                  Drag to pan
                </span>
              )}
            </div>
            <div className="flex items-center gap-1 sm:gap-2">
              {/* HTML Zoom Controls */}
              <BootstrapTooltip title="Zoom out HTML preview" placement="bottom">
                <button
                  onClick={htmlZoomOut}
                  disabled={htmlZoomLevel <= 0.25}
                  className="p-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  <ZoomOut className="h-4 w-4 text-gray-600" />
                </button>
              </BootstrapTooltip>

              <div className="px-2 sm:px-3 py-1 bg-white border border-gray-300 rounded-lg min-w-[40px] sm:min-w-[60px] text-center">
                <span className="text-xs sm:text-sm font-medium text-gray-700">
                  {Math.round(htmlZoomLevel * 100)}%
                </span>
              </div>

              <BootstrapTooltip title="Zoom in HTML preview" placement="bottom">
                <button
                  onClick={htmlZoomIn}
                  disabled={htmlZoomLevel >= 3}
                  className="p-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  <ZoomIn className="h-4 w-4 text-gray-600" />
                </button>
              </BootstrapTooltip>

              <BootstrapTooltip title="Reset HTML preview zoom and position" placement="bottom">
                <button
                  onClick={resetHtmlZoom}
                  className="p-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  <RotateCcw className="h-4 w-4 text-gray-600" />
                </button>
              </BootstrapTooltip>

              <BootstrapTooltip title="Refresh HTML preview" placement="bottom">
                <button
                  onClick={() => {
                    console.log('Refresh button clicked');
                    fetchScreenHTML(selectedScreen);
                  }}
                  className="p-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  <RefreshCw className="h-4 w-4 text-gray-600" />
                </button>
              </BootstrapTooltip>

              <BootstrapTooltip
                title={isAiTyping ? "Review disabled during AI generation" : "Review HTML preview"}
                placement="bottom"
              >
                <button
                  onClick={handleReviewClick}
                  disabled={isAiTyping}
                  className={`flex items-center gap-2 px-3 h-[32px] typography-caption font-weight-medium rounded-md shadow-sm transition-colors ${isAiTyping
                    ? 'bg-gray-200 text-gray-400 cursor-not-allowed'
                    : 'bg-primary text-white hover:bg-primary-600'
                    }`}
                >
                  <Eye className="h-4 w-4" />
                  Review
                </button>
              </BootstrapTooltip>
            </div>
          </div>

          {/* HTML Preview with zoom and pan */}
          <div
            ref={htmlPreviewRef}
            className="flex-1 overflow-hidden relative bg-white"
            style={{
              cursor: isHtmlDragging ? 'grabbing' : 'grab'
            }}
          >

            
            {/* Quick reset button when zoomed in */}
            {htmlZoomLevel > 1 && (
              <button
                onClick={resetHtmlZoom}
                className="absolute top-2 right-2 z-10 bg-black/70 text-white px-2 py-1 rounded text-xs hover:bg-black/80 transition-colors"
                title="Reset zoom and position"
              >
                Reset View
              </button>
            )}
            
            {/* Content container with proper zoom and pan */}
            <div
              className={`${isHtmlDragging ? '' : 'transition-transform duration-100 ease-out'}`}
              style={{
                transform: `translate(${htmlPosition.x}px, ${htmlPosition.y}px)`,
                width: '100%',
                height: '100%',
                position: 'relative'
              }}
              onMouseDown={handleHtmlMouseDown}
              onMouseMove={handleHtmlMouseMove}
              onMouseUp={handleHtmlMouseUp}
              onMouseLeave={handleHtmlMouseUp}
              onDoubleClick={handleHtmlDoubleClick}
            >
              {/* Add CSS styles for HTML content */}
              <style dangerouslySetInnerHTML={{
                __html: `
                  .html-preview-content {
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                    line-height: 1.5;
                    color: #333;
                    width: 100%;
                    height: auto;
                    min-height: 100vh;
                    overflow: visible;
                  }
                  .html-preview-content * {
                    box-sizing: border-box;
                  }
                  .html-preview-content img, .html-preview-content svg, .html-preview-content canvas {
                    max-width: 100%;
                    height: auto;
                  }
                  /* Ensure all styles from the original HTML are preserved */
                  .html-preview-content div, .html-preview-content span, .html-preview-content p {
                    margin: 0;
                    padding: 0;
                  }
                `
              }} />
              {/* HTML content with proper zoom and styling */}
              <div
                ref={htmlContentContainerRef}
                className="html-preview-content w-full h-full"
                style={{
                  transform: `scale(${htmlZoomLevel})`,
                  transformOrigin: '0 0',
                  width: `${100 / htmlZoomLevel}%`,
                  height: `${100 / htmlZoomLevel}%`,
                  position: 'relative',
                  backgroundColor: 'white'
                }}
                dangerouslySetInnerHTML={{
                  __html: (() => {
                    // If htmlContent is a complete HTML document, extract and preserve all content
                    if (htmlContent.includes('<!DOCTYPE html>') || htmlContent.includes('<html')) {
                      // Extract head content for styles
                      const headMatch = htmlContent.match(/<head[^>]*>([\s\S]*)<\/head>/i);
                      const bodyMatch = htmlContent.match(/<body[^>]*>([\s\S]*)<\/body>/i);
                      
                      let styles = '';
                      if (headMatch) {
                        // Extract style tags from head
                        const styleMatches = headMatch[1].match(/<style[^>]*>([\s\S]*)<\/style>/gi);
                        if (styleMatches) {
                          styles = styleMatches.map(style => {
                            const styleContent = style.match(/<style[^>]*>([\s\S]*)<\/style>/i);
                            return styleContent ? styleContent[1] : '';
                          }).join('\n');
                        }
                      }
                      
                      // Also extract any inline styles
                      const inlineStyleMatches = htmlContent.match(/<style[^>]*>([\s\S]*)<\/style>/gi);
                      if (inlineStyleMatches) {
                        const inlineStyles = inlineStyleMatches.map(style => {
                          const styleContent = style.match(/<style[^>]*>([\s\S]*)<\/style>/i);
                          return styleContent ? styleContent[1] : '';
                        }).join('\n');
                        styles += '\n' + inlineStyles;
                      }
                      
                      let bodyContent = htmlContent;
                      if (bodyMatch) {
                        bodyContent = bodyMatch[1];
                      }
                      
                      // Return content with preserved styles
                      return `
                        <style>
                          ${styles}
                          /* Additional preview styles */
                          * {
                            box-sizing: border-box;
                          }
                          body {
                            margin: 0;
                            padding: 0;
                            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                          }
                        </style>
                        <div style="width: 100%; height: auto; min-height: 100vh;">
                          ${bodyContent}
                        </div>
                      `;
                    } else {
                      // If it's just HTML content without html/body tags, wrap it properly
                      return `
                        <style>
                          * {
                            box-sizing: border-box;
                          }
                          body {
                            margin: 0;
                            padding: 0;
                            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                          }
                        </style>
                        <div style="width: 100%; height: auto; min-height: 100vh;">
                          ${htmlContent}
                        </div>
                      `;
                    }
                  })()
                }}
              />
            </div>
          </div>
        </div>
      );
    }

    // Show error message if there was an error fetching HTML
    if (htmlError) {
      return (
        <div className="w-full h-full flex items-center justify-center flex-col gap-4 relative">
          <AlertTriangle className="h-8 w-8 text-orange-500" />
          <p className="text-gray-600 text-sm font-medium">Failed to load preview</p>
          <p className="text-gray-500 text-xs text-center max-w-md">
            {htmlError}
          </p>
          <button
            onClick={() => {
              fetchScreenHTML(selectedScreen);
            }}
            className="px-4 py-2 bg-primary text-white rounded-md text-sm hover:bg-primary-600 transition-colors flex items-center gap-2"
          >
            <RefreshCw className="h-4 w-4" />
            Retry
          </button>
        </div>
      );
    }

    // Check if the current screen has been processed already
    const isScreenAlreadyProcessed = currentScreenId && processedScreenIds.has(currentScreenId);

    // If the screen is already processed but we don't have HTML content,
    // try to fetch it and show loading state
    if (isScreenAlreadyProcessed && !htmlContent && !isLoadingHTML) {
      // Trigger HTML fetch
      setTimeout(() => {
        fetchScreenHTML(selectedScreen);
      }, 0);

      return (
        <div className="w-full h-full flex items-center justify-center flex-col gap-4">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <p className="text-gray-600 text-sm font-medium">Loading preview...</p>
          <p className="text-gray-500 text-xs text-center max-w-md">
            Loading saved HTML preview for this screen...
          </p>
        </div>
      );
    }

    // Show play button as fallback when no HTML content is available and screen is not processed
    return (
      <div className="w-full h-full flex flex-col justify-center items-center space-y-4 relative">
        <button
          onClick={handlePlayIconClick}
          className="w-16 h-16 bg-primary hover:bg-primary-600 rounded-full flex items-center justify-center text-white shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed"
          title={
            !figmaData || figmaData?.length === 0
              ? "No Figma screen found to convert into code."
              : !setUploadedAttachments || !setInputValue
                ? "Missing required data to start code generation."
                : getCurrentProcessingScreen(projectId) === null && isAiTyping
                  ? "Code generation is already in progress, please wait until it completes."
                  : getCurrentProcessingScreen(projectId) === null ?
                    "Extract the selected Figma screen into code" :
                    getCurrentProcessingScreen(projectId) !== currentScreenId
                      ? "Code generation is currently running for another screen. Please wait until it completes.."
                      : ""
          }
          disabled={!figmaData || figmaData?.length === 0 || !setUploadedAttachments || !setInputValue || isAiTyping}
        >
          <Play className="w-8 h-8 ml-1" fill="currentColor" />
        </button>
        <p className="text-sm text-gray-700 text-center px-4">
          {getCurrentProcessingScreen(projectId) === null && isAiTyping
            ? "Code generation is already in progress, please wait until it completes."
            :
            getCurrentProcessingScreen(projectId) === null ?
              "Click the play button to generate code from the selected figma screen" :
              getCurrentProcessingScreen(projectId) !== currentScreenId
                ?
                "Code generation is currently running for another screen. Please wait until it completes." :
                ""}
        </p>
      </div>
    );
  };

  // Update the renderFigmaPanel function to fix panel expansion direction indicators
  const renderFigmaPanel = () => {
    // Show a single loader for the entire figma page
    if (figmaLoader || isInitializing) {
      return (
        <div className="flex flex-col items-center justify-center h-full w-full gap-3">
          <div className="w-10 h-10 relative">
            <div className="absolute inset-0 rounded-full border-2 border-gray-200"></div>
            <div className="absolute inset-0 rounded-full border-t-2 border-r-2 border-primary animate-spin"></div>
          </div>
          <p className="text-gray-600 text-sm font-medium">
            Loading Figma designs…
          </p>
        </div>
      );
    }

    // Show no designs message when loading is complete but no data is found
    if ((!figmaData || figmaData.length === 0) && !isInitializing) {
      return (
        <div className="w-full h-full flex items-center justify-center flex-col gap-4 bg-gray-50 relative">
          <div className="p-4 rounded-full bg-white shadow-md">
            <Figma className="h-16 w-16 text-[#ff9800] animate-pulse" />
          </div>
          <div className="text-center">
            <p className="text-gray-600 text-lg font-medium mb-2">No Figma Designs</p>
            <p className="text-gray-500 text-sm max-w-md">
              No Figma designs have been added to this project yet. Add some designs to see them here.
            </p>
          </div>
        </div>
      );
    }

    return (
      <div className="w-full h-full flex overflow-hidden relative">
        {/* Figma Panel - Left Side */}
        <div
          className={`${leftPanelFullscreen ? 'w-full' : rightPanelFullscreen ? 'hidden' : 'w-1/2'} h-full overflow-hidden transition-all duration-300 relative`}
        >
          {renderFigmaOnlyPanel()}
        </div>

        {/* Center Divider with dynamic arrow - Only visible in split mode */}
        {!leftPanelFullscreen && !rightPanelFullscreen && (
          <div
            className="absolute left-1/2 top-0 transform -translate-x-1/2 h-full z-10 w-12 flex items-center justify-center"
            onMouseMove={(e) => {
              // Determine which side of the divider the mouse is on
              const rect = e.currentTarget.getBoundingClientRect();
              const mouseX = e.clientX - rect.left;
              setHoverSide(mouseX < rect.width / 2 ? 'left' : 'right');
            }}
            onMouseLeave={() => setHoverSide(null)}
          >
            {/* Only show button when hovering */}
            {hoverSide && (
              <button
                onClick={() => {
                  if (hoverSide === 'left') {
                    setLeftPanelFullscreen(true);
                    setRightPanelFullscreen(false);
                  } else {
                    setLeftPanelFullscreen(false);
                    setRightPanelFullscreen(true);
                  }
                }}
                className="p-2 bg-white rounded-full shadow-md hover:bg-gray-100 transition-colors"
                title={hoverSide === 'left' ? "Expand left panel" : "Expand right panel"}
              >
                {/* When hovering left side, show right arrow (to indicate right panel will be hidden) */}
                {/* When hovering right side, show left arrow (to indicate left panel will be hidden) */}
                {hoverSide === 'left' ?
                  <ChevronRight className="h-5 w-5 text-gray-600" /> :
                  <ChevronLeft className="h-5 w-5 text-gray-600" />
                }
              </button>
            )}
          </div>
        )}

        {/* Main Panel - Right Side */}
        <div
          className={`${rightPanelFullscreen ? 'w-full' : leftPanelFullscreen ? 'hidden' : 'w-1/2'} h-full border-r border-gray-200 flex-shrink-0 overflow-hidden transition-all duration-300 relative`}
        >
          {renderMainPanelContent()}
        </div>

        {/* Return to split view button - Only shown when a panel is fullscreen */}
        {(leftPanelFullscreen || rightPanelFullscreen) && (
          <div className={`absolute ${leftPanelFullscreen ? 'right-0' : 'left-0'} top-1/2 transform -translate-y-1/2 z-10`}>
            <button
              onClick={() => {
                setLeftPanelFullscreen(false);
                setRightPanelFullscreen(false);
              }}
              className={`p-2 bg-white shadow-md hover:bg-gray-100 transition-colors border border-gray-200 ${leftPanelFullscreen ? 'rounded-l-md border-r-0' : 'rounded-r-md border-l-0'}`}
              title="Return to split view"
            >
              {/* When left panel is fullscreen, show left arrow to return */}
              {/* When right panel is fullscreen, show right arrow to return */}
              {leftPanelFullscreen ?
                <ChevronLeft className="h-5 w-5 text-gray-600" /> :
                <ChevronRight className="h-5 w-5 text-gray-600" />
              }
            </button>
          </div>
        )}
      </div>
    );
  };

  // Render main content panel
  const renderMainPanel = () => {
    if (containers.length === 0) {
      return (
        <div className="w-full h-full flex items-center justify-center">
          <p className="text-gray-500 text-sm">No containers available</p>
        </div>
      );
    }

    if (!selectedContainer || !container) {
      return (
        <div className="w-full h-full flex items-center justify-center">
          <p className="text-gray-500 text-sm">Loading container...</p>
        </div>
      );
    }

    if (container.status === 'running' && container.url) {
      if (isCheckingUrl || urlStatus === 'checking') {
        return (
          <div className="w-full h-full flex items-center justify-center flex-col gap-4">
            <div className="w-10 h-10 relative">
              <div className="absolute inset-0 rounded-full border-2 border-gray-200"></div>
              <div className="absolute inset-0 rounded-full border-t-2 border-r-2 border-primary animate-spin"></div>
            </div>
            <p className="text-gray-600 text-sm">
              Checking service availability...
            </p>
          </div>
        );
      }

      if (urlStatus === 'error' || iframeError) {
        const getErrorMessage = () => {
          switch (errorType) {
            case '404':
              return {
                icon: <AlertTriangle className="h-16 w-16 text-primary" />,
                title: "Project not available",
                description: "We couldn't find the project right now. It may be starting up — please check back shortly."
              };
            case '502':
              return {
                icon: <AlertTriangle className="h-16 w-16 text-primary" />,
                title: "Project is loading",
                description: "The project is taking longer than usual to respond. Please wait a moment and try again."
              };
            default:
              return {
                icon: <AlertTriangle className="h-16 w-16 text-primary" />,
                title: "Something went wrong",
                description: "We're having trouble connecting to the project. Please try again shortly."
              };
          }
        };

        const errorInfo = getErrorMessage();

        return (
          <div className="w-full h-full flex items-center justify-center flex-col gap-4">
            {errorInfo.icon}
            <p className="text-gray-600 text-sm font-medium text-center">
              {errorInfo.title}
            </p>
            <p className="text-gray-500 text-xs text-center max-w-md leading-relaxed">
              {errorInfo.description}
            </p>

            <button
              onClick={refreshIframe}
              className="px-6 py-2 bg-primary text-white rounded-md text-sm hover:bg-primary-600 transition-colors flex items-center gap-2"
            >
              <RefreshCw className="h-4 w-4" />
              Refresh
            </button>
          </div>
        );
      }

      if (urlStatus === 'success') {
        const finalUrl = processPreviewUrl(container.url);

        return (
          <div className="w-full h-full flex flex-col overflow-hidden">
            <div className="flex-1 relative overflow-hidden">
              <iframe
                ref={iframeRef}
                key={`${container.name}-${iframeKey}`}
                src={finalUrl}
                className="w-full h-full border-none overflow-hidden"
                title={`Preview - ${container.name}`}
                sandbox="allow-same-origin allow-scripts allow-forms allow-popups"
                allow="microphone; camera; midi; encrypted-media;"
                onError={() => {
                  setIframeError(true);
                  setErrorType('other');
                }}
              />

              <button
                onClick={refreshIframe}
                className="absolute top-4 right-4 p-2 bg-black/10 hover:bg-black/20 rounded-md transition-colors backdrop-blur-sm"
                title="Refresh preview"
              >
                <RefreshCw className="h-4 w-4 text-gray-700" />
              </button>
            </div>
          </div>
        );
      }
    }

    if (container.status === 'building' || container.status === 'starting') {
      return (
        <div className="w-full h-full flex items-center justify-center flex-col gap-4">
          <div className="w-10 h-10 relative">
            <div className="absolute inset-0 rounded-full border-2 border-gray-200"></div>
            <div className="absolute inset-0 rounded-full border-t-2 border-r-2 border-primary animate-spin"></div>
          </div>
          <p className="text-gray-600 text-sm">
            {container.name} is {container.status}...
          </p>
          <p className="text-gray-500 text-xs text-center max-w-md">
            Please wait while the container starts up. This may take a few moments.
          </p>
        </div>
      );
    }

    return (
      <div className="w-full h-full flex items-center justify-center flex-col gap-4">
        {container.status === 'failed' || container.status === 'error' ? (
          <>
            <AlertCircle className="h-8 w-8 text-red-500" />
            <p className="text-gray-600 text-sm">
              {container.name} failed to start
            </p>
            {container.error && (
              <p className="text-gray-500 text-xs max-w-md text-center">
                {container.error}
              </p>
            )}
          </>
        ) : container.status === ContainerStatusTypes.NOT_STARTED ? (
          <>
            <div className="w-10 h-10 relative">
              <div className="absolute inset-0 rounded-full border-2 border-gray-200"></div>
              <div className="absolute inset-0 rounded-full border-t-2 border-r-2 border-primary animate-spin"></div>
            </div>
            <p className="text-gray-500 text-xs text-center max-w-md">
              The container is being automatically started. Please wait a moment.
            </p>
          </>
        ) : (
          <>
            <Square className="h-8 w-8 text-gray-400" />
            <p className="text-gray-600 text-sm">
              {container.name} is {container.status || 'not started'}
            </p>
          </>
        )}
      </div>
    );
  };

  // Main render based on showMode
  if (showMode === "figma") {
    return renderFigmaPanel();
  }

  if (showMode === "main") {
    return (
      <div className="w-full h-full">
        {renderMainPanel()}
      </div>
    );
  }

  return (
    <div className="w-full h-full flex overflow-hidden">
      {renderMainPanel()}
    </div>
  );
};

export default FigmaPreviewPanel;