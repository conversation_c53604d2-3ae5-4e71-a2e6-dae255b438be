import React, { useState } from 'react';
import { 
  Trash2, 
  AlertCircle,
  CheckCircle,
  Loader2,
  Smartphone
} from 'lucide-react';

const AppetizeLogs = ({ 
  appetizeState, 
  onClearLogs 
}) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const getStatusIcon = () => {
    switch (appetizeState.connectionStatus) {
      case 'connected':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'connecting':
        return <Loader2 className="h-4 w-4 text-blue-500 animate-spin" />;
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      default:
        return <Smartphone className="h-4 w-4 text-gray-400" />;
    }
  };

  const getStatusText = () => {
    switch (appetizeState.connectionStatus) {
      case 'connected':
        return appetizeState.session ? 'Session Active' : 'Connected - Waiting for session';
      case 'connecting':
        return 'Connecting...';
      case 'error':
        return 'Error';
      default:
        return 'Disconnected';
    }
  };

  const getLevelColor = (level) => {
    switch (level) {
      case 'error':
        return 'bg-red-50 border-red-500 text-red-800';
      case 'warn':
        return 'bg-yellow-50 border-yellow-500 text-yellow-800';
      case 'network':
        return 'bg-blue-50 border-blue-500 text-blue-800';
      case 'interaction':
        return 'bg-purple-50 border-purple-500 text-purple-800';
      default:
        return 'bg-gray-50 border-gray-300 text-gray-800';
    }
  };

  if (!appetizeState.isAppetizeUrl) return null;

  return (
    <div className="bg-white border border-gray-200 rounded-lg shadow-sm">
      {/* Header */}
      <div 
        className="flex items-center justify-between p-3 cursor-pointer hover:bg-gray-50"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <div className="flex items-center gap-3">
          <div className="flex items-center gap-2">
            {getStatusIcon()}
            <span className="text-sm font-medium text-gray-900">
              Appetize Debug Monitor
            </span>
          </div>
          <span className="text-xs text-gray-500">
            {getStatusText()}
          </span>
          {appetizeState.session && (
            <div className="flex items-center gap-1">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span className="text-xs text-green-600 font-medium">Recording</span>
            </div>
          )}
        </div>
        
        <div className="flex items-center gap-2">
          {appetizeState.debugLogs.length > 0 && (
            <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
              {appetizeState.debugLogs.length} events
            </span>
          )}
          <span className="text-gray-400">
            {isExpanded ? '−' : '+'}
          </span>
        </div>
      </div>

      {/* Error Display */}
      {appetizeState.logError && (
        <div className="px-3 pb-3 border-t border-gray-100">
          <div className="mt-3 p-2 bg-red-50 border border-red-200 rounded text-sm text-red-700">
            {appetizeState.logError}
          </div>
        </div>
      )}

      {/* Expanded Content */}
      {isExpanded && (
        <div className="border-t border-gray-100">
          {/* Controls */}
          <div className="flex items-center justify-between p-3 bg-gray-50">
            <div className="flex items-center gap-2">
              <span className="text-xs text-gray-600">
                Session: {appetizeState.session?.token ? 
                  `${appetizeState.session.token.substring(0, 8)}...` : 
                  'None'
                }
              </span>
            </div>
            <button
              onClick={onClearLogs}
              disabled={appetizeState.debugLogs.length === 0}
              className="p-1 hover:bg-gray-200 rounded disabled:opacity-50 disabled:cursor-not-allowed"
              title="Clear Logs"
            >
              <Trash2 className="h-4 w-4 text-gray-600" />
            </button>
          </div>

          {/* Logs Display */}
          <div className="max-h-64 overflow-y-auto p-3">
            {appetizeState.debugLogs.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <Smartphone className="h-8 w-8 mx-auto mb-2 text-gray-400" />
                <p className="text-sm">No events captured yet</p>
                <p className="text-xs mt-1">
                  {appetizeState.connectionStatus === 'connecting'
                    ? "Setting up Appetize connection..."
                    : appetizeState.session 
                    ? "Start interacting with the app to see logs" 
                    : "Waiting for Appetize session to start"
                  }
                </p>
              </div>
            ) : (
              <div className="space-y-1 font-mono text-xs">
                {appetizeState.debugLogs.map((log) => (
                  <div 
                    key={log.id}
                    className={`p-2 rounded border-l-2 ${getLevelColor(log.level)}`}
                  >
                    <div className="flex items-start gap-2">
                      <span className="text-gray-500 text-xs shrink-0">
                        {new Date(log.timestamp).toLocaleTimeString()}
                      </span>
                      <span className={`font-semibold text-xs shrink-0 ${
                        log.level === 'error' ? 'text-red-600' :
                        log.level === 'warn' ? 'text-yellow-600' :
                        log.level === 'network' ? 'text-blue-600' :
                        log.level === 'interaction' ? 'text-purple-600' :
                        'text-blue-600'
                      }`}>
                        {log.level.toUpperCase()}
                      </span>
                      <span className="break-all">{log.message}</span>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default AppetizeLogs;