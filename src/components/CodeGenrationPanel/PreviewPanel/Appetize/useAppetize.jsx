import { useState, useCallback, useRef, useEffect } from 'react';

const useAppetize = (wsConnection, currentTaskId) => {
  const [appetizeState, setAppetizeState] = useState({
    isAppetizeUrl: false,
    sdkLoaded: false,
    client: null,
    session: null,
    debugLogs: [],
    connectionStatus: 'disconnected', // disconnected, connecting, connected, error
    lastSentLogId: null,
    logError: null
  });

  // Refs for cleanup and state management
  const clientRef = useRef(null);
  const sessionRef = useRef(null);
  const sendIntervalRef = useRef(null);

  // Helper: Add debug log
  const addDebugLog = useCallback((message, level = 'info', raw = null) => {
    const logEntry = {
      id: Date.now() + Math.random(),
      timestamp: new Date().toISOString(),
      message,
      level,
      raw
    };

    setAppetizeState(prev => ({
      ...prev,
      debugLogs: [...prev.debugLogs.slice(-99), logEntry] // Keep last 100 logs
    }));

    return logEntry;
  }, []);

  // Helper: Send logs to WebSocket
  const sendLogsToWebSocket = useCallback((forceAll = false) => {
    const { debugLogs, lastSentLogId } = appetizeState;
    
    // Filter logs to send (only new ones unless forceAll is true)
    const logsToSend = forceAll 
      ? debugLogs 
      : debugLogs.filter(log => !lastSentLogId || log.id > lastSentLogId);

    if (logsToSend.length === 0) {
      return false; // Nothing to send
    }

    // Send if WebSocket is ready
    if (wsConnection?.readyState === WebSocket.OPEN && currentTaskId) {
      try {
        wsConnection.send(JSON.stringify({
          type: "sync_appetize_logs",
          task_id: currentTaskId,
          input_data: {
            logs: logsToSend.map(log => ({
              timestamp: log.timestamp,
              level: log.level,
              message: log.message
            }))
          }
        }));

        // Update last sent log ID
        const lastLog = logsToSend[logsToSend.length - 1];
        setAppetizeState(prev => ({
          ...prev,
          lastSentLogId: lastLog.id
        }));

        console.log(`Sent ${logsToSend.length} Appetize logs`);
        return true;
      } catch (error) {
        console.error('Failed to send logs:', error);
        return false;
      }
    }
    
    return false;
  }, [appetizeState, wsConnection, currentTaskId]);

  // Check if URL is Appetize
  const isAppetizeUrl = useCallback((url) => {
    if (!url || typeof url !== 'string') return false;
    return url.includes('appetize.io') || url.includes('app.appetize.io');
  }, []);

  // Load Appetize SDK
  const loadAppetizeSDK = useCallback(() => {
    return new Promise((resolve, reject) => {
      // Check if already loaded
      if (window.appetize) {
        resolve(window.appetize);
        return;
      }

      // Load the SDK
      const script = document.createElement('script');
      script.src = 'https://js.appetize.io/embed.js';
      script.async = true;
      script.onload = () => {
        if (window.appetize) {
          resolve(window.appetize);
        } else {
          reject(new Error('Appetize SDK failed to load'));
        }
      };
      script.onerror = () => reject(new Error('Failed to load Appetize SDK'));
      document.head.appendChild(script);
    });
  }, []);

  // Setup session event listeners
  const setupSessionListeners = useCallback((session) => {
    addDebugLog(`Session started - Token: ${session.token}`, 'info');
    
    // Core event listeners for debugging
    session.on('log', (logData) => {
      const message = logData.message || String(logData);
      const level = logData.level || 'info';
      addDebugLog(message, level, logData);
    });

    session.on('interaction', (interaction) => {
      const message = `User interaction: ${interaction.type || 'touch'} ${
        interaction.coordinates ? `at (${interaction.coordinates.x}, ${interaction.coordinates.y})` : ''
      }`;
      addDebugLog(message, 'interaction', interaction);
    });

    session.on('network', (networkData) => {
      const message = `Network: ${networkData.method || 'GET'} ${networkData.url || 'unknown URL'}`;
      addDebugLog(message, 'network', networkData);
    });

    session.on('error', (error) => {
      const message = `Session Error: ${error.message || error}`;
      addDebugLog(message, 'error', error);
    });

    session.on('inactivityWarning', (data) => {
      addDebugLog(`Inactivity warning: ${data.secondsRemaining}s remaining`, 'warn');
      // Send heartbeat to prevent timeout
      try {
        session.heartbeat();
        addDebugLog('Heartbeat sent', 'info');
      } catch (error) {
        addDebugLog(`Heartbeat error: ${error.message}`, 'error');
      }
    });

    session.on('end', () => {
      addDebugLog('Session ended', 'info');
      
      // Force send ALL logs when session ends
      setTimeout(() => {
        sendLogsToWebSocket(true); // Force send all logs
      }, 200); // Small delay to ensure 'session ended' log is included
      
      // Clean up state
      setAppetizeState(prev => ({
        ...prev,
        session: null,
        connectionStatus: 'disconnected'
      }));
      
      sessionRef.current = null;
    });
  }, [addDebugLog, sendLogsToWebSocket]);

  // Initialize Appetize client
  const initializeAppetizeClient = useCallback(async (iframeElement) => {
    if (!iframeElement || !appetizeState.isAppetizeUrl) return;
    
    // Prevent re-initialization if already connecting or connected
    if (appetizeState.connectionStatus === 'connecting' || 
        appetizeState.connectionStatus === 'connected' ||
        clientRef.current) {
      return;
    }

    try {
      addDebugLog('Initializing Appetize client...', 'info');
      setAppetizeState(prev => ({ ...prev, connectionStatus: 'connecting' }));

      // Load SDK if needed
      if (!appetizeState.sdkLoaded) {
        await loadAppetizeSDK();
        setAppetizeState(prev => ({ ...prev, sdkLoaded: true }));
        addDebugLog('SDK loaded', 'info');
      }

      // Wait a moment for iframe to be ready
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Get the client
      const iframeId = 'appetize-preview-iframe';
      iframeElement.id = iframeId;
      
      const client = await window.appetize.getClient(`#${iframeId}`);
      clientRef.current = client;
      
      setAppetizeState(prev => ({ 
        ...prev, 
        client,
        connectionStatus: 'connected'
      }));
      
      addDebugLog('Client connected', 'info');

      // Listen for session events
      client.on('session', (session) => {
        sessionRef.current = session;
        setAppetizeState(prev => ({ ...prev, session }));
        setupSessionListeners(session);
      });

      // Check for existing session
      if (client.session) {
        sessionRef.current = client.session;
        setAppetizeState(prev => ({ ...prev, session: client.session }));
        setupSessionListeners(client.session);
      } else {
        addDebugLog('Waiting for session to start...', 'info');
      }

    } catch (error) {
      console.error('Failed to initialize Appetize:', error);
      addDebugLog(`Initialization error: ${error.message}`, 'error');
      setAppetizeState(prev => ({ 
        ...prev, 
        connectionStatus: 'error',
        logError: error.message
      }));
    }
  }, [appetizeState.isAppetizeUrl, appetizeState.sdkLoaded, appetizeState.connectionStatus, loadAppetizeSDK, addDebugLog, setupSessionListeners]);

  // Send logs every 15 seconds
  useEffect(() => {
    if (!appetizeState.isAppetizeUrl) return;

    // Send any existing logs immediately
    if (appetizeState.debugLogs.length > 0 && !appetizeState.lastSentLogId) {
      sendLogsToWebSocket();
    }

    // Set up interval for periodic sending
    const interval = setInterval(() => {
      if (appetizeState.debugLogs.length > 0) {
        sendLogsToWebSocket();
      }
    }, 15000); // 15 seconds

    sendIntervalRef.current = interval;
    return () => clearInterval(interval);
  }, [appetizeState.isAppetizeUrl, sendLogsToWebSocket]);

  // Send logs on page hide/unload
  useEffect(() => {
    const handlePageHide = () => {
      if (appetizeState.debugLogs.length > 0) {
        sendLogsToWebSocket(true); // Force send all logs
      }
    };

    document.addEventListener('visibilitychange', handlePageHide);
    window.addEventListener('beforeunload', handlePageHide);
    window.addEventListener('pagehide', handlePageHide);

    return () => {
      document.removeEventListener('visibilitychange', handlePageHide);
      window.removeEventListener('beforeunload', handlePageHide);
      window.removeEventListener('pagehide', handlePageHide);
    };
  }, [appetizeState.debugLogs, sendLogsToWebSocket]);

  // Update URL detection
  const updateUrl = useCallback((url) => {
    const isAppetize = isAppetizeUrl(url);
    
    // Only update if the URL type actually changed
    if (isAppetize !== appetizeState.isAppetizeUrl) {
      setAppetizeState(prev => ({ ...prev, isAppetizeUrl: isAppetize }));
      
      if (!isAppetize) {
        // Clean up if not Appetize URL
        if (sessionRef.current) {
          try {
            sessionRef.current.end();
          } catch (error) {
            console.warn('Error ending session:', error);
          }
        }
        
        // Send any remaining logs before cleanup
        if (appetizeState.debugLogs.length > 0) {
          sendLogsToWebSocket(true);
        }
        
        // Reset state
        clientRef.current = null;
        sessionRef.current = null;
        if (sendIntervalRef.current) {
          clearInterval(sendIntervalRef.current);
        }
        
        setAppetizeState({
          isAppetizeUrl: false,
          sdkLoaded: false,
          client: null,
          session: null,
          debugLogs: [],
          connectionStatus: 'disconnected',
          lastSentLogId: null,
          logError: null
        });
      }
    }
  }, [isAppetizeUrl, appetizeState.isAppetizeUrl, appetizeState.debugLogs, sendLogsToWebSocket]);

  // Clear logs
  const clearLogs = useCallback(() => {
    setAppetizeState(prev => ({ ...prev, debugLogs: [] }));
  }, []);

  // Force send all logs
  const forceSendLogs = useCallback(() => {
    sendLogsToWebSocket(true);
  }, [sendLogsToWebSocket]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      // Send any remaining logs
      const currentLogs = appetizeState.debugLogs;
      if (currentLogs.length > 0) {
        // Use function form to avoid dependency on appetizeState
        const cleanupSendLogs = () => {
          try {
            if (wsConnection?.readyState === WebSocket.OPEN && currentTaskId) {
              wsConnection.send(JSON.stringify({
                type: "sync_appetize_logs",
                task_id: currentTaskId,
                input_data: {
                  logs: currentLogs.map(log => ({
                    timestamp: log.timestamp,
                    level: log.level,
                    message: log.message
                  }))
                }
              }));
            }
          } catch (error) {
            console.error('Failed to send logs during cleanup:', error);
          }
        };
        
        cleanupSendLogs();
      }
      
      // Clean up session
      const currentSession = sessionRef.current;
      if (currentSession) {
        try {
          currentSession.end();
        } catch (error) {
          console.warn('Error ending session on unmount:', error);
        }
      }
      
      // Clear interval
      if (sendIntervalRef.current) {
        clearInterval(sendIntervalRef.current);
      }
    };
  }, [currentTaskId, wsConnection]);

  return {
    appetizeState,
    initializeAppetizeClient,
    updateUrl,
    clearLogs,
    forceSendLogs,
    sendLogsToWebSocket: (forceAll = false) => sendLogsToWebSocket(forceAll)
  };
};

export default useAppetize;