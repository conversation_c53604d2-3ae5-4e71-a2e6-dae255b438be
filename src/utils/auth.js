"use client"
import Cookies from 'js-cookie';
import { jwtDecode } from "jwt-decode";

export async function setC<PERSON>ie(name, value, options) {
  try {
    Cookies.set(name, value, options);
  } catch (error) {
    
    throw error;
  }
}

export async function getCookie(name) {
  try {
    const cookie = Cookies.get(name);
    return cookie;
  } catch (error) {
    
    throw error;
  }
}

export async function removeCookie(name) {
  try {
    Cookies.remove(name);
  } catch (error) {
    
    throw error;
  }
}

export async function decrypt(input) {
    const decoded = jwtDecode(input);
    return decoded;
}


export function decryptToken(token) {
  if (!token) return null;
  return jwtDecode(token);
}

// Check if JWT token is expired
export function isTokenExpired(token) {
  if (!token) return true;
  try {
    const decoded = jwtDecode(token);
    const currentTime = Date.now() / 1000;
    return decoded.exp < currentTime;
  } catch (error) {
    console.warn('Error decoding token:', error);
    return true;
  }
}

// Check if token is available and valid
export function isTokenValid(token) {
  return token && !isTokenExpired(token);
}
