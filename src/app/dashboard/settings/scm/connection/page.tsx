// @ts-nocheck
// @ts-ignore
"use client"
import React, { useEffect, useState, useContext } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { listRepositories, getRepositoryDetails } from '@/utils/repositoryAPI';
import TableComponent from '@/components/SimpleTable/ScmTableBackendPagination';
import { ScmRepoLoader } from "@/components/UIComponents/Loaders/LoaderGroup";
import EmptyStateView from '@/components/Modal/EmptyStateModal';
import { AlertContext } from "@/components/NotificationAlertService/AlertList";
import { formatDateTime } from '@/utils/datetime';
import { DynamicButton } from "@/components/UIComponents/Buttons/DynamicButton";
import { Building2, Github } from 'lucide-react';

type Repository = {
  repositoryName: string;
  repositoryId: string;
  path: string;
  web_url: string;
  organization: string;
  description: string | null;
  default_branch: string;
  visibility: string;
  ssh_url: string;
  http_url: string;
  created_at: string | null;
  last_activity_at: string | null;
  scm_type: string;
};

const Page: React.FC = () => {
  const [repositories, setRepositories] = useState<Repository[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [clientId, setClientId] = useState<string>('');
  const [showOrgRequestModal, setShowOrgRequestModal] = useState<boolean>(false);
  const [hasClickedGithub, setHasClickedGithub] = useState<boolean>(false);
  const [pagination, setPagination] = useState({
    currentPage: 1,
    totalPages: 1,
    perPage: 10,
    totalCount: 0
  });
  const searchParams = useSearchParams();
  const scm_type = searchParams.get('scmType') || "github";
  const scm_id = decodeURIComponent(searchParams.get('scmId') || "");
  
  const rowsPerPage = 10;
  const router = useRouter();
  const { showAlert } = useContext(AlertContext);

  const service = scm_type;

  // Separate function to fetch client_id using getRepositoryDetails
  const fetchClientId = async () => {
    try {
      const response = await getRepositoryDetails(service, {
        scmId: scm_id,
        decrypt: true
      });
      
      if (response?.client_id) {
        setClientId(response.client_id);
      }
    } catch (error) {
      console.error("Failed to fetch client ID:", error);
    }
  };

  const handleRequestOrganization = () => {
    if (!clientId) {
      showAlert("Client ID not available. Please try refreshing the page.", "error");
      return;
    }
    setShowOrgRequestModal(!showOrgRequestModal);
  };

  const handleGoToGithub = () => {
    // Open GitHub settings page for the specific application
    window.open(`https://github.com/settings/connections/applications/${clientId}`, '_blank');
    setHasClickedGithub(true);
  };

  const handleDone = async () => {
    setShowOrgRequestModal(false);
    setHasClickedGithub(false); // Reset for next time
    // Refresh repositories data
    await fetchRepositories();
    showAlert("Repository list refreshed successfully!", "success");
  };

  const fetchRepositories = async (page = 1) => {
    try {
      setIsLoading(true);
      
      // Use listRepositories for getting repositories list with pagination
      const response = await listRepositories(service, scm_id, true, page, rowsPerPage);
      
      if (response?.status === "success" && response?.data?.repositories) {
        // Format repositories with properly formatted dates
        const formattedRepositories = response.data.repositories.map((repo) => ({
          ...repo,
          created_at: repo.created_at ? formatDateTime(repo.created_at, true) : null,
          last_activity_at: repo.last_activity_at ? formatDateTime(repo.last_activity_at, true) : null
        }));
        
        setRepositories(formattedRepositories);
        
        // Fetch client_id separately using getRepositoryDetails
        await fetchClientId();
        
        // Update pagination data
        setPagination({
          currentPage: response.data.pagination?.current_page || 1,
          totalPages: response.data.pagination?.total_pages || 1,
          perPage: response.data.pagination?.per_page || 10,
          totalCount: response.data.total_repositories || 0
        });
      } else {
        setRepositories([]);
        setPagination({
          currentPage: 1,
          totalPages: 1,
          perPage: 10,
          totalCount: 0
        });
      }
    } catch (error) {
      
      showAlert("Failed to fetch repositories", "error");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchRepositories();
  }, []);

  const handleRowClick = (id: string) => {
    
  };

  const handlePageChange = (page: number) => {
    fetchRepositories(page);
  };

  if(isLoading) {
    return <ScmRepoLoader />;
  }

  return (
    <div>
      <div className="flex items-center typography-body-sm text-gray-600 space-x-2 mb-6">
        <span
          className="text-gray-800 hover:text-primary hover:underline cursor-pointer transition-colors duration-300"
          onClick={() => router.push('/dashboard/settings/scm')}
        >
          SCM Providers
        </span>
        <span className="text-gray-400">{'>'}</span>
        <span
          className="text-gray-800 hover:text-primary hover:underline cursor-pointer transition-colors duration-300"
          onClick={() => router.push(`/dashboard/settings/scm/${service}`)} 
        >
          SCM Configurations
        </span>
        <span className="text-gray-400">{'>'}</span>
        <span className="text-gray-800 font-weight-medium">Repositories</span>
      </div>
      <div className='flex justify-between items-center'>
        <h1 className="typography-body-lg font-weight-semibold mt-4 mb-5">{`Repositories from ${scm_type.charAt(0).toUpperCase() + scm_type.slice(1)}`}</h1>
        <div className="relative">
          <DynamicButton
            variant={clientId ? "orange" : "primaryOutline"}
            size="default"
            icon={Building2}
            text="Request organization"
            onClick={handleRequestOrganization}
            tooltip="Request access to an organization"
            disabled={!clientId}
          />
          
          {/* Organization Request Popup */}
          {showOrgRequestModal && (
            <div className="absolute top-full right-0 mt-2 w-96 bg-white rounded-lg shadow-lg border border-gray-200 z-50">
              <div className="p-4">
                <div className="text-center mb-4">
                  <h3 className="typography-body-lg font-weight-semibold text-gray-900 mb-2">
                    Request Organization
                  </h3>
                </div>

                <div className="space-y-3 mb-4">
                  <div className="flex items-start space-x-3">
                    <div className="bg-primary text-white rounded-full w-5 h-5 flex items-center justify-center typography-caption font-weight-semibold flex-shrink-0 mt-0.5">
                      1
                    </div>
                    <div>
                      <p className="typography-body-sm font-weight-medium text-gray-900">
                        Go to Application Page
                      </p>
                      <p className="typography-caption text-gray-600">
                        Click "Go to Github" to redirect to the organization access page
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-3">
                    <div className="bg-primary text-white rounded-full w-5 h-5 flex items-center justify-center typography-caption font-weight-semibold flex-shrink-0 mt-0.5">
                      2
                    </div>
                    <div>
                      <p className="typography-body-sm font-weight-medium text-gray-900">
                        Find & Click Request Button
                      </p>
                      <p className="typography-caption text-gray-600">
                        Look for the "Request" button on the page to request organization access
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-3">
                    <div className="bg-primary text-white rounded-full w-5 h-5 flex items-center justify-center typography-caption font-weight-semibold flex-shrink-0 mt-0.5">
                      3
                    </div>
                    <div>
                      <p className="typography-body-sm font-weight-medium text-gray-900">
                        Wait for Approval
                      </p>
                      <p className="typography-caption text-gray-600">
                        After admin approval, click "Done" here to refresh repositories
                      </p>
                    </div>
                  </div>
                </div>

                <div className="flex space-x-2">
                  <DynamicButton
                    variant={hasClickedGithub ? "primaryOutline" : "green"}
                    size="small"
                    icon={Github}
                    text="Go to Github"
                    onClick={handleGoToGithub}
                    className="flex-1"
                  />
                  <DynamicButton
                    variant={hasClickedGithub ? "green" : "primaryOutline"}
                    size="small"
                    text="Done"
                    onClick={handleDone}
                    className="flex-1"
                  />
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
      {repositories.length === 0 ? (
        <div className="text-center text-gray-500 p-6">
          <EmptyStateView type='repoNotFound' onClick={() => {}}/>
        </div>
      ) : (
        <div className="mb-6">
          <TableComponent
            headers={[
              { key: 'repositoryName', label: 'Name' },
              { key: 'organization', label: 'Organization' },
              { key: 'visibility', label: 'Visibility' },
              { key: 'default_branch', label: 'Default Branch' },
              { key: 'created_at', label: 'Created' },
              { key: 'last_activity_at', label: 'Last Activity' },
            ]}
            data={repositories}
            onRowClick={handleRowClick}
            sortableColumns={{
              repositoryName: true,
              organization: true,
              visibility: true,
              default_branch: true,
              created_at: true,
              last_activity_at: true,
            }}
            itemsPerPage={rowsPerPage}
            title={`All Repositories (${pagination.totalCount})`}
            pagination={{
              currentPage: pagination.currentPage,
              totalPages: pagination.totalPages,
              onPageChange: handlePageChange
            }}
          />
        </div>
      )}
    </div>
  );
};

export default Page;