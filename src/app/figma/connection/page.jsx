"use client";
import React, { useState, useEffect } from 'react';
import { handleFigmaOAuthCallback } from "@/utils/FigmaAPI";
import Cookies from 'js-cookie';

const Page = () => {
  const [loading, setLoading] = useState(true);
  const [alertMessage, setAlertMessage] = useState(null);
  const [closingIn, setClosingIn] = useState(3);

  useEffect(() => {
    // Extract the 'code' and 'state' parameters from the URL
    const urlParams = new URLSearchParams(window.location.search);
    const code = urlParams.get('code');
    const state = urlParams.get('state');
    
    if (code) {
      handleFigmaCallback(code, state);
    } else {
      setLoading(false); // If there's no code, proceed without showing loading
    }
  }, []);

  const handleFigmaCallback = async (code, state) => {
    try {
      // Get userId and tenantId from cookies
      const userId = Cookies.get('userId');
      const tenantId = Cookies.get('tenant_id');
      
      // Call the API with the code, state, and get the response
      const figma_user_response = await handleFigmaOAuthCallback(code, state, userId, tenantId);
      
      // Save Figma user data in localStorage
      localStorage.setItem('figmaUserData', JSON.stringify(figma_user_response));

      // Remove 'code' from URL after successful connection
      const url = new URL(window.location.href);
      url.searchParams.delete('code');
      window.history.replaceState(null, '', url.toString());
      setLoading(false); // Hide loading after successful connection
      
      // Send message to parent window
      if (window.opener) {
        window.opener.postMessage('figma_connected', '*');
      }
      
      // Start countdown timer
      const interval = setInterval(() => {
        setClosingIn((prev) => {
          if (prev <= 1) {
            clearInterval(interval);
            window.close();
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
      
    } catch (error) {
      console.error('Figma OAuth callback error:', error);
      setAlertMessage({
        title: 'Error',
        content: 'Failed to connect to Figma.',
      });
      setLoading(false); // Hide loading on error
    }
  };

  return (
    <div className="flex justify-center items-center h-screen">
      {loading ? (
        <div className="card bg-gray-100 p-6 rounded-lg shadow-lg max-w-sm">
          <h2 className="typography-heading-4 font-weight-semibold text-center">Please Wait</h2>
          <p className="text-center mt-2">Your Figma connection is being processed...</p>
        </div>
      ) : (
        <div className="text-center">
          <h2 className="typography-heading-4 font-weight-semibold">
            {alertMessage ? alertMessage.title : "Figma connected successfully!"}
          </h2>
          <p className="mt-2">
            {alertMessage 
              ? alertMessage.content 
              : `You can close this window. Closing automatically in ${closingIn} seconds.`
            }
          </p>
        </div>
      )}
    </div>
  );
};

export default Page; 